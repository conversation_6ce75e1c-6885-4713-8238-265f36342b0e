# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\Downloads\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\Desktop\\asatu_new" PROJECT_DIR)

set(FLUTTER_VERSION "0.1.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\Downloads\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\Desktop\\asatu_new"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\Downloads\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\Desktop\\asatu_new\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\Desktop\\asatu_new"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\Desktop\\asatu_new\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\Desktop\\asatu_new\\.dart_tool\\package_config.json"
)
