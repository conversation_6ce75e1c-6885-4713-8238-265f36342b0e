{"indexes": [{"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "withdrawals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "requestedAt", "order": "DESCENDING"}]}, {"collectionGroup": "withdrawals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "requestedAt", "order": "DESCENDING"}]}, {"collectionGroup": "assets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "requiresAdminApproval", "order": "ASCENDING"}, {"fieldPath": "admin<PERSON><PERSON>ie<PERSON>", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}