import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:app_links/app_links.dart';

import 'firebase_web_init.dart';
import 'firebase_web_interop.dart'; // Import the web interop helper
import 'services/firebase_messaging_service.dart';
import 'services/firebase_in_app_messaging_service.dart';
import 'services/analytics_service.dart';
import 'services/stripe_payment_service.dart';
import 'services/notification_service.dart';
import 'services/deadline_service.dart';
import 'services/user_content_service.dart';
import 'services/data_performance_service.dart';
import 'services/background_task_service.dart';
import 'services/challenge_service.dart';

import 'services/notification_settings_service.dart';
import 'admin_panel.dart';
import 'analytics_dashboard_page.dart';
import 'asset_upload_page.dart';
import 'auth_page.dart';
import 'community_page.dart';
import 'detailed_post_page.dart';
import 'home_page.dart';
import 'notifications_page.dart';
import 'profile_page.dart';
import 'profile_setup_page.dart';
import 'public_profile_page.dart';
import 'splash_screen.dart';
import 'transaction_history_page.dart';
import 'utils/performance_optimizations.dart';
import 'providers/theme_provider.dart';
import 'providers/feature_provider.dart';
import 'providers/user_provider.dart';

// Define the background message handler
@pragma('vm:entry-point') // Needed to ensure the function can be called in the background
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Initialize Firebase for background handlers
  await FirebaseWebInit.initialize();

  // Print the message for debugging
  debugPrint('Handling a background message: ${message.messageId}');
  debugPrint('Message data: ${message.data}');
  debugPrint('Message notification: ${message.notification?.title}');
}

void main() {
  // Run the app with error handling
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize Firebase with web-specific handling
    try {
      // Initialize JavaScript interop functions for web platform
      if (kIsWeb) {
        FirebaseWebInterop.initializeInterop();
        debugPrint('Web interop functions initialized');
      }

      // Use our custom web initialization for better web compatibility
      final app = await FirebaseWebInit.initialize();
      debugPrint('Firebase initialized successfully with app: ${app.name}');

      // Initialize Crashlytics (only for non-web platforms)
      if (!kIsWeb) {
        FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;
        debugPrint('Crashlytics initialized for error reporting');
      }

      // Initialize Firebase Messaging Service
      try {
        // Set up background message handler
        FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

        // Initialize the messaging service
        await FirebaseMessagingService().initialize();
        debugPrint('Firebase Messaging Service initialized successfully');

        // Initialize the in-app messaging service
        await FirebaseInAppMessagingService().initialize();
        debugPrint('Firebase In-App Messaging Service initialized successfully');

        // Initialize Firebase Analytics
        await AnalyticsService().initialize();
        debugPrint('Firebase Analytics Service initialized successfully');

        // Log app_open event
        AnalyticsService().logEvent(
          name: 'app_open',
          parameters: {
            'platform': kIsWeb ? 'web' : defaultTargetPlatform.toString(),
            'app_version': '0.1.0',
          },
        );
      } catch (messagingError) {
        debugPrint('Error initializing Firebase Messaging: $messagingError');
      }

      // Initialize Firebase Auth with more robust error handling
      try {
        // Set persistence to LOCAL to ensure the user stays logged in
        await FirebaseAuth.instance.setPersistence(Persistence.LOCAL);

        // Disable reCAPTCHA verification for testing
        await FirebaseAuth.instance.setSettings(
          appVerificationDisabledForTesting: true,
        );
        debugPrint('Firebase Auth initialized successfully with persistence set to LOCAL and reCAPTCHA disabled');
      } catch (authError) {
        debugPrint('Error initializing Firebase Auth: $authError');
      }

      // Initialize Firestore with error handling
      try {
        // Just accessing the instance initializes it
        FirebaseFirestore.instance;
        debugPrint('Firestore initialized successfully');
      } catch (firestoreError) {
        debugPrint('Error initializing Firestore: $firestoreError');
      }

      // Initialize Google Sign-In with error handling
      try {
        final GoogleSignIn googleSignIn = GoogleSignIn(
          scopes: [
            'email',
            'profile',
          ],
        );

        // Test Google Sign-In initialization
        try {
          final isSignedIn = await googleSignIn.isSignedIn();
          debugPrint('Google Sign-In initialized successfully. Is signed in: $isSignedIn');
        } catch (signInStatusError) {
          debugPrint('Error checking Google Sign-In status: $signInStatusError');
        }
      } catch (googleSignInError) {
        debugPrint('Error initializing Google Sign-In: $googleSignInError');
      }

    } catch (e) {
      debugPrint('Error initializing Firebase: $e');
      if (!kIsWeb) {
        FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      }
    }

    // Set preferred orientations - only portrait for better performance
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Set system UI overlay style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    );

    // Enable performance optimizations for all modes
    // Disable debug checks that are expensive in debug mode only
    if (!kReleaseMode) {
      debugPrint = (String? message, {int? wrapWidth}) {};
    }

    // Apply performance optimizations
    PerformanceOptimizations.optimizeApp();

    // Initialize Stripe payment service
    try {
      await StripePaymentService().initialize();
      debugPrint('Stripe payment service initialized successfully');
    } catch (stripeError) {
      debugPrint('Error initializing Stripe payment service: $stripeError');
    }

    // Disable unnecessary debug flags
    if (!kDebugMode) {
      debugPrintRebuildDirtyWidgets = false;
    }

    // Initialize services
    final notificationSettingsService = NotificationSettingsService();
    await notificationSettingsService.initialize();

    // Initialize performance service for ultra-fast data loading
    final dataPerformanceService = DataPerformanceService();
    dataPerformanceService.initialize();

    // Preload critical data in background
    if (FirebaseAuth.instance.currentUser != null) {
      dataPerformanceService.preloadData(FirebaseAuth.instance.currentUser!.uid);
    }

    debugPrint('Data Performance Service initialized for 100x faster loading');

    // Initialize background task manager for account deletion processing
    BackgroundTaskManager.initialize();

    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => ThemeProvider()),
          ChangeNotifierProvider(create: (_) => FeatureProvider()),
          ChangeNotifierProvider.value(value: notificationSettingsService),
          ChangeNotifierProvider(
            create: (_) {
              final provider = UserProvider();
              // Initialize in a way that doesn't block app startup
              Future.microtask(() {
                return provider.initialize().catchError((e) {
                  FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
                });
              });
              return provider;
            },
          ),
          // Add UserContentService provider
          Provider<UserContentService>(
            create: (_) => UserContentService(),
          ),
        ],
        child: const MyApp(),
      ),
    );
  }, (error, stackTrace) {
    // Report errors to Crashlytics (only for non-web platforms)
    try {
      if (!kIsWeb) {
        FirebaseCrashlytics.instance.recordError(error, stackTrace);
      }
    } catch (e) {
      debugPrint('Failed to record error to Crashlytics: $e');
    }

    // Also print to console for debugging
    debugPrint('Caught error: $error');
  });
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  bool _initialized = false;
  bool _profileSetupCompleted = false;
  bool _isAuthenticated = false;
  StreamSubscription? _linkSubscription;
  late AppLinks _appLinks;
  String? _pendingPostId; // Store post ID for deep link navigation after app is ready

  @override
  void initState() {
    super.initState();
    _checkAuthAndProfileSetup();
    _initDeepLinks();

    // Check for approaching deadlines
    _checkDeadlines();
  }

  @override
  void dispose() {
    _linkSubscription?.cancel();
    super.dispose();
  }

  // Initialize deep link handling
  void _initDeepLinks() {
    debugPrint('Initializing deep links...');

    _appLinks = AppLinks();

    // Handle initial link when app is opened from a link
    _handleInitialLink();

    // Handle incoming links when app is already running
    _linkSubscription = _appLinks.uriLinkStream.listen(
      (Uri uri) {
        debugPrint('Received deep link: ${uri.toString()}');
        _handleDeepLink(uri.toString());
      },
      onError: (err) {
        debugPrint('Deep link error: $err');
      },
    );
  }

  // Handle initial link when app is opened from a link
  Future<void> _handleInitialLink() async {
    try {
      final Uri? initialUri = await _appLinks.getInitialAppLink();
      if (initialUri != null) {
        final String initialLink = initialUri.toString();
        debugPrint('Initial deep link: $initialLink');
        // Wait for app to be fully initialized before handling the link
        Future.delayed(const Duration(seconds: 2), () {
          _handleDeepLink(initialLink);
        });
      }
    } catch (e) {
      debugPrint('Error getting initial link: $e');
    }
  }

  // Handle deep link navigation
  void _handleDeepLink(String link) {
    debugPrint('Handling deep link: $link');

    try {
      final Uri uri = Uri.parse(link);
      debugPrint('Parsed URI: ${uri.toString()}');
      debugPrint('URI scheme: ${uri.scheme}');
      debugPrint('URI host: ${uri.host}');
      debugPrint('URI path: ${uri.path}');
      debugPrint('URI query parameters: ${uri.queryParameters}');

      // Handle different deep link patterns
      if (uri.path.startsWith('/post/') || uri.path.contains('/post/')) {
        // Extract post ID from the path
        String postId = uri.path.replaceFirst('/post/', '');
        // Handle case where path might be just "post/id"
        if (postId.isEmpty && uri.pathSegments.length >= 2 && uri.pathSegments[0] == 'post') {
          postId = uri.pathSegments[1];
        }
        debugPrint('Extracted post ID: $postId');
        if (postId.isNotEmpty) {
          _navigateToPost(postId);
        } else {
          debugPrint('Post ID is empty, navigating to home');
          _navigateToHome();
        }
      } else if (uri.path.startsWith('/profile/') || uri.path.contains('/profile/')) {
        // Extract user ID from the path
        String userId = uri.path.replaceFirst('/profile/', '');
        // Handle case where path might be just "profile/id"
        if (userId.isEmpty && uri.pathSegments.length >= 2 && uri.pathSegments[0] == 'profile') {
          userId = uri.pathSegments[1];
        }
        debugPrint('Extracted user ID: $userId');
        if (userId.isNotEmpty) {
          _navigateToProfile(userId);
        } else {
          debugPrint('User ID is empty, navigating to home');
          _navigateToHome();
        }
      } else {
        debugPrint('Unknown deep link pattern: ${uri.path}');
        // Default to home page
        _navigateToHome();
      }
    } catch (e) {
      debugPrint('Error parsing deep link: $e');
      _navigateToHome();
    }
  }

  // Navigate to a specific post
  void _navigateToPost(String postId) {
    if (!mounted) return;

    debugPrint('_navigateToPost called with postId: $postId');
    debugPrint('_isAuthenticated: $_isAuthenticated, _profileSetupCompleted: $_profileSetupCompleted');

    // Store the post ID for later navigation if app is not ready
    if (!_isAuthenticated || !_profileSetupCompleted) {
      debugPrint('App not ready, storing postId for later: $postId');
      // Store the post ID to navigate to it once the app is ready
      _pendingPostId = postId;
      return;
    }

    // Wait for the app to be fully loaded and context to be available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      // Wait a bit more to ensure the navigation context is ready
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (!mounted) return;

        try {
          debugPrint('Navigating to detailed post page with postId: $postId');
          // Directly fetch and open the post without navigating to home first
          _fetchAndOpenPost(postId);
        } catch (e) {
          debugPrint('Error navigating to post: $e');
          // Fallback: try to navigate to home
          try {
            Navigator.of(context).pushNamedAndRemoveUntil(
              '/home',
              (route) => false,
            );
          } catch (homeError) {
            debugPrint('Error navigating to home: $homeError');
          }
        }
      });
    });
  }

  // Fetch post data and open in detailed post page
  Future<void> _fetchAndOpenPost(String postId) async {
    // Show loading indicator
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    try {
      debugPrint('Fetching post data for postId: $postId');

      // Fetch post from Firebase with timeout
      final postDoc = await FirebaseFirestore.instance
          .collection('posts')
          .doc(postId)
          .get()
          .timeout(const Duration(seconds: 10));

      // Hide loading indicator
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (!postDoc.exists) {
        debugPrint('Post not found in Firebase');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Post not found'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      final data = postDoc.data() as Map<String, dynamic>;
      debugPrint('Post data fetched successfully');

      // Create post map with basic fields first
      final Timestamp timestamp = data['createdAt'] as Timestamp? ?? Timestamp.now();
      final DateTime postDate = timestamp.toDate();
      final bool hasImage = data['imageUrl'] != null;
      final String postType = data['type'] as String? ?? 'regular';

      String authorName = data['authorName'] ?? data['author'] ?? 'Unknown';
      String authorImage = data['authorImage'] ?? data['authorAvatar'] ?? '';
      String authorId = data['authorId'] ?? data['userId'] ?? '';

      final Map<String, dynamic> post = {
        'id': postDoc.id,
        'author': authorName,
        'authorName': authorName,
        'authorImage': authorImage,
        'authorId': authorId,
        'content': data['content'] ?? '',
        'imageUrl': hasImage ? data['imageUrl'] : null,
        'likes': data['likesCount'] ?? data['likes'] ?? 0,
        'comments': data['commentsCount'] ?? data['comments'] ?? 0,
        'shares': data['sharesCount'] ?? data['shares'] ?? 0,
        'time': postDate,
        'type': postType,
        'isLiked': false, // Will be updated by the detailed page
        'isSaved': false, // Will be updated by the detailed page
      };

      // Add type-specific fields
      if (postType == 'poll') {
        post['pollOptions'] = data['pollOptions'] ?? [];
        post['expiryDate'] = data['pollEndDate'];
        post['pollEndDate'] = data['pollEndDate'];
      } else if (postType == 'code') {
        post['codeContent'] = data['code'] ?? '';
        post['codeLanguage'] = data['codeLanguage'] ?? 'text';
        post['code'] = data['code'] ?? '';
      }

      if (mounted) {
        debugPrint('Navigating to DetailedPostPage');
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => DetailedPostPage(post: post),
          ),
        );

        // Fetch author info in background after navigation
        _fetchAuthorInfoInBackground(post, authorId, authorName, authorImage);
      }
    } catch (e) {
      // Hide loading indicator if still showing
      if (mounted) {
        Navigator.of(context).pop();
      }

      debugPrint('Error fetching post: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading post: ${e.toString().contains('timeout') ? 'Request timed out' : 'Network error'}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Fetch author info in background (non-blocking)
  Future<void> _fetchAuthorInfoInBackground(Map<String, dynamic> post, String authorId, String authorName, String authorImage) async {
    if ((authorName == 'Unknown' || authorImage.isEmpty) && authorId.isNotEmpty) {
      try {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(authorId)
            .get()
            .timeout(const Duration(seconds: 5));

        if (userDoc.exists) {
          // Update the post data if needed (this is optional since the page is already loaded)
          debugPrint('Author info fetched in background');
        }
      } catch (e) {
        debugPrint('Error fetching author info in background: $e');
      }
    }
  }

  // Navigate to a specific profile
  void _navigateToProfile(String userId) {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isAuthenticated && _profileSetupCompleted) {
        Navigator.of(context).pushNamed(
          '/public_profile',
          arguments: {'userId': userId},
        );
      } else {
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/auth',
          (route) => false,
        );
      }
    });
  }

  // Navigate to home
  void _navigateToHome() {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isAuthenticated && _profileSetupCompleted) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/home',
          (route) => false,
        );
      } else {
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/auth',
          (route) => false,
        );
      }
    });
  }

  // Check for approaching deadlines and send notifications
  Future<void> _checkDeadlines() async {
    try {
      // Wait for authentication check to complete
      await Future.delayed(const Duration(seconds: 5));

      // Only check deadlines if user is authenticated
      if (_isAuthenticated) {
        final DeadlineService deadlineService = DeadlineService();
        await deadlineService.checkApproachingDeadlines();
        debugPrint('Deadline check completed');

        // Also check challenge deadlines
        final ChallengeService challengeService = ChallengeService();
        await challengeService.checkAndRefundExpiredChallenges();
        debugPrint('Challenge deadline check completed');

        // Schedule periodic deadline checks
        Timer.periodic(const Duration(hours: 12), (timer) async {
          if (mounted && _isAuthenticated) {
            await deadlineService.checkApproachingDeadlines();
            await challengeService.checkAndRefundExpiredChallenges();
            debugPrint('Periodic deadline check completed');
          } else {
            // Cancel the timer if the widget is no longer mounted or user is not authenticated
            timer.cancel();
          }
        });
      }
    } catch (e) {
      debugPrint('Error checking deadlines: $e');
    }
  }

  // Check authentication state and profile setup
  Future<void> _checkAuthAndProfileSetup() async {
    try {
      debugPrint('Checking authentication state and profile setup...');

      // IMPORTANT: First check if we have a cached auth state in shared preferences
      // This is a more reliable way to check if the user is logged in
      final prefs = await SharedPreferences.getInstance();
      final cachedAuthState = prefs.getBool('is_authenticated') ?? false;
      debugPrint('Cached auth state: $cachedAuthState');

      // Force refresh the current user to ensure we have the latest data
      User? currentUser = FirebaseAuth.instance.currentUser;

      // If we have a cached auth state but no current user, try to persist the session
      if (cachedAuthState && currentUser == null) {
        debugPrint('Cached auth state is true but no current user, attempting to restore session');

        // Wait a moment to allow Firebase to initialize fully
        await Future.delayed(const Duration(milliseconds: 500));

        // Check again after delay
        currentUser = FirebaseAuth.instance.currentUser;
        debugPrint('After delay, current user: ${currentUser?.uid}');
      }

      if (currentUser != null) {
        try {
          await currentUser.reload();
          // Get the refreshed user
          currentUser = FirebaseAuth.instance.currentUser;
          debugPrint('User refreshed: ${currentUser?.uid}');

          // Save authentication state to shared preferences
          await prefs.setBool('is_authenticated', true);
        } catch (e) {
          debugPrint('Error refreshing user: $e');
          // Continue with the current user even if refresh fails
        }
      } else if (cachedAuthState) {
        // If we still don't have a current user but have a cached auth state,
        // we'll try one more approach - check if we have cached credentials
        debugPrint('Attempting to restore session from cached credentials');

        try {
          // Try to get cached email and password (if available)
          final cachedEmail = prefs.getString('auth_email');
          final cachedToken = prefs.getString('auth_token');

          if (cachedEmail != null && cachedToken != null) {
            debugPrint('Found cached credentials, attempting to sign in');

            // Try to sign in with cached credentials
            try {
              // Use the token to sign in
              await FirebaseAuth.instance.signInWithCustomToken(cachedToken);
              currentUser = FirebaseAuth.instance.currentUser;
              debugPrint('Restored session for user: ${currentUser?.uid}');
            } catch (signInError) {
              debugPrint('Error signing in with cached token: $signInError');
              // Clear cached auth state if sign-in fails
              await prefs.setBool('is_authenticated', false);
            }
          }
        } catch (cacheError) {
          debugPrint('Error accessing cached credentials: $cacheError');
        }
      }

      // Check if user is logged in
      if (currentUser != null) {
        debugPrint('User is logged in: ${currentUser.uid}');
        // User is logged in with Firebase
        _isAuthenticated = true;

        // Check if user document exists in Firestore
        try {
          final userDoc = await FirebaseFirestore.instance
              .collection('users')
              .doc(currentUser.uid)
              .get();

          if (userDoc.exists) {
            final userData = userDoc.data();
            debugPrint('User document exists: ${userData != null}');

            // Check if user is banned
            if (userData?['isBanned'] == true) {
              debugPrint('User is banned, signing out');
              // Sign out banned user
              await FirebaseAuth.instance.signOut();
              _isAuthenticated = false;
              _profileSetupCompleted = false;

              // Clear cached auth state
              await prefs.setBool('is_authenticated', false);

              // Show banned message after UI is built
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Your account has been banned. Please contact support.'),
                      backgroundColor: Colors.red,
                      duration: Duration(seconds: 5),
                    ),
                  );
                }
              });
            } else {
              // User is not banned, proceed normally
              _profileSetupCompleted = userData?['profileSetupCompleted'] ?? false;
              debugPrint('Profile setup completed: $_profileSetupCompleted');

              // Cache the email for future session restoration
              if (currentUser.email != null) {
                await prefs.setString('auth_email', currentUser.email!);
              }

              // If profile setup status is missing, check if we have username as a fallback
              if (_profileSetupCompleted == false && userData?['username'] != null) {
                _profileSetupCompleted = true;
                debugPrint('Profile setup inferred from username presence');

                // Update the profileSetupCompleted flag in Firestore
                await FirebaseFirestore.instance
                    .collection('users')
                    .doc(currentUser.uid)
                    .update({'profileSetupCompleted': true});
              }
            }
          } else {
            debugPrint('User document does not exist, creating one');
            _profileSetupCompleted = false;

            // Create a basic user document if it doesn't exist
            await FirebaseFirestore.instance
                .collection('users')
                .doc(currentUser.uid)
                .set({
                  'uid': currentUser.uid,
                  'email': currentUser.email,
                  'displayName': currentUser.displayName ?? 'User',
                  'photoURL': currentUser.photoURL,
                  'emailVerified': currentUser.emailVerified,
                  'createdAt': FieldValue.serverTimestamp(),
                  'updatedAt': FieldValue.serverTimestamp(),
                  'profileSetupCompleted': false,
                  'isAdmin': false,
                  'isBanned': false,
                  'lastLogin': FieldValue.serverTimestamp(),
                  'balance': 0.0,
                  'totalEarnings': 0.0,
                  'pendingWithdrawals': 0.0,
                  'unreadNotifications': 0,
                }, SetOptions(merge: true));
          }
        } catch (e) {
          debugPrint('Error checking user document: $e');
          _profileSetupCompleted = false;
        }
      } else {
        debugPrint('No user logged in');
        // No user logged in
        _isAuthenticated = false;
        _profileSetupCompleted = false;

        // Clear cached auth state
        await prefs.setBool('is_authenticated', false);
      }

      // Try to get user provider as a backup check
      try {
        if (!mounted) return;

        final userProvider = Provider.of<UserProvider>(context, listen: false);
        if (userProvider.isLoggedIn) {
          debugPrint('UserProvider indicates user is logged in');
          _isAuthenticated = true;
          _profileSetupCompleted = userProvider.isProfileComplete;
          debugPrint('Profile setup from UserProvider: $_profileSetupCompleted');

          // Update cached auth state
          await prefs.setBool('is_authenticated', true);
        }
      } catch (e) {
        // Continue with what we already determined
        debugPrint('Error accessing UserProvider: $e');
      }

      // Update state
      if (mounted) {
        setState(() {
          _initialized = true;
        });
      }

      debugPrint('Auth check complete - Authenticated: $_isAuthenticated, Profile Setup: $_profileSetupCompleted');

      // Handle pending post navigation if app is now ready
      if (_isAuthenticated && _profileSetupCompleted && _pendingPostId != null) {
        debugPrint('App is now ready, navigating to pending post: $_pendingPostId');
        final postId = _pendingPostId!;
        _pendingPostId = null; // Clear the pending post ID

        // Wait a moment for the UI to be ready
        Future.delayed(const Duration(milliseconds: 1500), () {
          if (mounted) {
            _navigateToPost(postId);
          }
        });
      }
    } catch (e) {
      debugPrint('Error checking auth and profile setup: $e');
      if (!kIsWeb) {
        FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
      }

      if (mounted) {
        setState(() {
          _isAuthenticated = false;
          _profileSetupCompleted = false;
          _initialized = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    // We're not using featureProvider directly, but we need to listen to it
    Provider.of<FeatureProvider>(context, listen: false);

    // Use const where possible for better performance
    return MaterialApp(
      title: 'Asatu',
      debugShowCheckedModeBanner: false,
      themeMode: themeProvider.themeMode,
      theme: themeProvider.getThemeData(false),
      darkTheme: themeProvider.getThemeData(true),

      // Fixed to English
      locale: const Locale('en'),
      supportedLocales: const [Locale('en')],

      builder: (context, child) {
        // Apply text scaling factor limit for better UI consistency
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: MediaQuery.of(context).textScaler.clamp(
              minScaleFactor: 0.8 * themeProvider.fontSizeScale,
              maxScaleFactor: 1.2 * themeProvider.fontSizeScale,
            ),
          ),
          child: child!,
        );
      },
      // Use const widgets for better performance
      home: !_initialized
          ? const SplashScreen()
          : !_isAuthenticated
              ? const AuthPage()
              : _profileSetupCompleted
                  ? const HomePage()
                  : const ProfileSetupPage(),
      routes: {
        '/auth': (context) => const AuthPage(),
        '/home': (context) => const HomePage(),
        '/public_profile': (context) => const PublicProfilePage(),
        '/admin': (context) => const AdminPanel(),
        '/profile_setup': (context) => const ProfileSetupPage(),
        '/asset_upload': (context) => const AssetUploadPage(),
        '/community': (context) => const CommunityPage(),
        '/notifications': (context) => const NotificationsPage(),
        '/transaction_history': (context) => const TransactionHistoryPage(),
      },
      // Check auth state on route changes
      onGenerateRoute: (settings) {
        // If user is authenticated but profile is not complete, redirect to profile setup
        if (_isAuthenticated && !_profileSetupCompleted &&
            settings.name != '/profile_setup' && settings.name != '/auth') {
          return MaterialPageRoute(
            builder: (context) => const ProfileSetupPage(),
          );
        }

        // Handle community page with arguments
        if (settings.name == '/community') {
          final args = settings.arguments as Map<String, dynamic>?;
          final String? postId = args?['postId'];
          return MaterialPageRoute(
            builder: (context) => CommunityPage(postId: postId),
          );
        }

        // Handle specific routes that might be missing from the routes table
        if (settings.name == '/login') {
          return MaterialPageRoute(
            builder: (context) => const AuthPage(),
          );
        }

        return null;
      },

      // Handle unknown routes
      onUnknownRoute: (settings) {
        debugPrint('Unknown route: ${settings.name}');
        // Redirect to auth page if not authenticated, otherwise to home
        return MaterialPageRoute(
          builder: (context) => !_isAuthenticated
              ? const AuthPage()
              : const HomePage(),
        );
      },
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with AutomaticKeepAliveClientMixin {
  int _currentIndex = 0;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseInAppMessagingService _inAppMessagingService = FirebaseInAppMessagingService();

  // Use indexed stack for better performance - keeps state but only builds visible page
  final List<Widget> _pages = const [
    HomePageContent(),
    AssetUploadPage(),
    CommunityPage(),
    AnalyticsDashboardPage(),
    ProfilePage(),
  ];

  @override
  void initState() {
    super.initState();
    // Listen for Firebase messages
    _setupFirebaseMessaging();
  }

  // Set up Firebase messaging
  void _setupFirebaseMessaging() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('Got a message in the foreground!');
      debugPrint('Message data: ${message.data}');

      if (message.notification != null) {
        debugPrint('Message also contained a notification:');
        debugPrint('Title: ${message.notification!.title}');
        debugPrint('Body: ${message.notification!.body}');

        // Show in-app message using our service
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _inAppMessagingService.showInAppMessage(
              context: context,
              title: message.notification!.title ?? 'New Message',
              body: message.notification!.body ?? '',
              data: message.data,
              onView: () {
                // Handle message tap based on type
                final String type = message.data['type'] ?? 'default';
                _handleMessageAction(type, message.data);
              },
            );
          }
        });
      }
    });
  }

  // Handle message action based on type
  void _handleMessageAction(String type, Map<String, dynamic> data) {
    switch (type) {
      case 'notification':
        // Navigate to notifications page
        Navigator.pushNamed(context, '/notifications');
        break;
      case 'asset':
        // Navigate to asset details
        final String? assetId = data['assetId'];
        if (assetId != null) {
          // Navigate to asset details
          Navigator.pushNamed(
            context,
            '/asset_detail',
            arguments: {'assetId': assetId}
          );
        }
        break;
      case 'profile':
        // Navigate to profile page
        setState(() {
          _currentIndex = 4; // Profile tab index
        });
        break;
      case 'message':
        // Navigate to messaging page
        Navigator.pushNamed(context, '/messaging');
        break;
      case 'community':
        // Navigate to community page
        setState(() {
          _currentIndex = 2; // Community tab index
        });
        break;
      default:
        // Default action - show notifications
        Navigator.pushNamed(context, '/notifications');
        break;
    }
  }

  @override
  bool get wantKeepAlive => true; // Keep page state alive when switching tabs



  @override
  Widget build(BuildContext context) {
    super.build(context); // Must call super.build for AutomaticKeepAliveClientMixin
    return Scaffold(
      // Use IndexedStack instead of directly accessing the page
      // This keeps all pages in memory but only renders the visible one
      body: IndexedStack(
        index: _currentIndex,
        children: _pages,
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(15),
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            type: BottomNavigationBarType.fixed,
            backgroundColor: Colors.white,
            selectedItemColor: const Color(0xFF6A11CB),
            unselectedItemColor: Colors.grey[400],
            selectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
            elevation: 0,
            items: [
              const BottomNavigationBarItem(
                icon: Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Icon(Icons.home_rounded),
                ),
                label: 'Home',
              ),
              const BottomNavigationBarItem(
                icon: Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Icon(Icons.upload_file_rounded),
                ),
                label: 'Upload',
              ),
              // Community tab with notification badge
              BottomNavigationBarItem(
                icon: Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: StreamBuilder<int>(
                    stream: NotificationService().getUnreadNotificationCount(_auth.currentUser?.uid ?? ''),
                    builder: (context, snapshot) {
                      final int unreadCount = snapshot.data ?? 0;

                      return Stack(
                        children: [
                          const Icon(Icons.people_rounded),
                          if (unreadCount > 0)
                            Positioned(
                              right: 0,
                              top: 0,
                              child: Container(
                                padding: const EdgeInsets.all(1),
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                constraints: const BoxConstraints(
                                  minWidth: 10,
                                  minHeight: 10,
                                ),
                                child: unreadCount > 9
                                  ? const Text(
                                      '9+',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 7,
                                      ),
                                      textAlign: TextAlign.center,
                                    )
                                  : Text(
                                      unreadCount.toString(),
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 8,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                              ),
                            ),
                        ],
                      );
                    },
                  ),
                ),
                label: 'Community',
              ),
              const BottomNavigationBarItem(
                icon: Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Icon(Icons.analytics_rounded),
                ),
                label: 'Analytics',
              ),
              // Profile tab without message notification badge
              const BottomNavigationBarItem(
                icon: Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Icon(Icons.person_rounded),
                ),
                label: 'Profile',
              ),
            ],
          ),
        ),
      ),
    );
  }
}
