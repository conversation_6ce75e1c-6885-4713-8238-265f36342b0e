import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show SystemUiOverlayStyle, Clipboard, ClipboardData;
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:image_picker/image_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'messaging_page.dart';
import 'notifications_page.dart';
import 'utils/performance_optimizations.dart';
import 'utils/responsive_layout.dart';
import 'services/user_content_service.dart';
import 'services/messaging_service.dart';
import 'services/notification_service.dart';
import 'services/data_performance_service.dart';
import 'services/post_update_service.dart';
import 'services/challenge_service.dart';
import 'widgets/mention_text.dart';
import 'public_profile_page.dart';
import 'models/challenge_model.dart';
import 'challenge_detail_page.dart';

// Full-screen search page
class SearchPage extends StatefulWidget {
  const SearchPage({Key? key}) : super(key: key);

  @override
  SearchPageState createState() => SearchPageState();
}

class SearchPageState extends State<SearchPage> {
  final TextEditingController searchController = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();
  List<Map<String, dynamic>> searchResults = [];
  bool isSearching = false;
  Timer? searchDebounceTimer;

  // Format count for display (e.g., 1200 -> 1.2K)
  String _formatCount(dynamic count) {
    if (count == null) return '0';

    final int numCount = count is int ? count : int.tryParse(count.toString()) ?? 0;

    if (numCount >= 1000000) {
      return '${(numCount / 1000000).toStringAsFixed(1)}M';
    } else if (numCount >= 1000) {
      return '${(numCount / 1000).toStringAsFixed(1)}K';
    } else {
      return numCount.toString();
    }
  }

  @override
  void initState() {
    super.initState();
    // Request focus after page is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    searchFocusNode.dispose();
    searchDebounceTimer?.cancel();
    super.dispose();
  }

  // Function to search users
  void performSearch(String query) {
    // Cancel any previous search timer
    searchDebounceTimer?.cancel();

    if (query.isEmpty) {
      setState(() {
        searchResults = [];
        isSearching = false;
      });
      return;
    }

    // Set searching state
    setState(() {
      isSearching = true;
    });

    // Store current query
    final String currentQuery = query;

    // Debounce search
    searchDebounceTimer = Timer(const Duration(milliseconds: 500), () async {
      try {
        // Get the UserContentService
        final userContentService = UserContentService();

        // Search for users
        final results = await userContentService.searchUsers(currentQuery);

        // Update state
        if (searchController.text == currentQuery && mounted) {
          setState(() {
            searchResults = results;
            isSearching = false;
          });

          // Debug log
          debugPrint('Search results: ${results.length} users found');
          for (var user in results) {
            debugPrint('User: ${user['name']} (${user['username']})');
          }
        }
      } catch (e) {
        debugPrint('Error searching users: $e');
        if (searchController.text == currentQuery && mounted) {
          setState(() {
            searchResults = [];
            isSearching = false;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: TextField(
          controller: searchController,
          focusNode: searchFocusNode,
          decoration: InputDecoration(
            hintText: 'Search by name or username...',
            border: InputBorder.none,
            hintStyle: GoogleFonts.poppins(
              color: Colors.grey[400],
            ),
            suffixIcon: searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear, color: Colors.grey),
                    onPressed: () {
                      setState(() {
                        searchController.clear();
                        searchResults = [];
                      });
                      searchFocusNode.requestFocus();
                    },
                  )
                : null,
          ),
          onChanged: performSearch,
          textInputAction: TextInputAction.search,
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: buildSearchResults(),
    );
  }

  Widget buildSearchResults() {
    // Show loading indicator
    if (searchController.text.isNotEmpty && searchResults.isEmpty && isSearching) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 40),
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6A11CB)),
            ),
            const SizedBox(height: 16),
            Text(
              'Searching...',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    // Show no results message
    if (searchController.text.isNotEmpty && searchResults.isEmpty && !isSearching) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 40),
            Icon(Icons.search, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No users found',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try a different search term',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    // Show empty state if no search
    if (searchController.text.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 40),
            Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Search for users',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Type a name or username to find people',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    // Show search results in Instagram-style UI
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
      itemCount: searchResults.length,
      itemBuilder: (context, index) {
        final user = searchResults[index];

        // Create an Instagram-style search result item
        return InkWell(
          onTap: () {
            // Navigate to public profile page
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PublicProfilePage(userId: user['id']),
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // User avatar
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.grey[200]!,
                      width: 0.5,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(30),
                    child: Image.network(
                      user['avatar'],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[200],
                          child: Center(
                            child: Text(
                              user['name'].toString().isNotEmpty
                                ? user['name'].toString().substring(0, 1).toUpperCase()
                                : '?',
                              style: GoogleFonts.poppins(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[500],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // User info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Full name with verification badge
                      Row(
                        children: [
                          Text(
                            user['name'],
                            style: GoogleFonts.poppins(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(width: 4),
                          if (user['isVerified'] == true)
                            const Icon(
                              Icons.verified,
                              color: Colors.blue,
                              size: 14,
                            ),
                          if (user['isElite'] == true)
                            Container(
                              margin: const EdgeInsets.only(left: 4),
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [Color(0xFF6A11CB), Color(0xFF2575FC)],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'Elite',
                                style: GoogleFonts.poppins(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                        ],
                      ),
                      // Username
                      Text(
                        '@${user['username']}',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      // Bio or stats
                      if (user['bio'] != null && user['bio'].toString().isNotEmpty)
                        Text(
                          user['bio'],
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        )
                      else
                        Text(
                          '${_formatCount(user['followers'])} followers · ${_formatCount(user['posts'])} posts',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class CommunityPage extends StatefulWidget {
  final String? postId;

  const CommunityPage({super.key, this.postId});

  @override
  CommunityPageState createState() => CommunityPageState();
}

class CommunityPageState extends State<CommunityPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _postController = TextEditingController();
  final TextEditingController _commentController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final DataPerformanceService _performanceService = DataPerformanceService();
  final PostUpdateService _postUpdateService = PostUpdateService();
  final ChallengeService _challengeService = ChallengeService();

  // Filter state variables for challenges
  String _selectedChallengeCategory = 'All';
  String _selectedPrizeRange = 'All';
  bool _showFilters = false;

  // FAB visibility state
  bool _showFab = true;
  ScrollController? _challengesScrollController;

  final List<Map<String, dynamic>> _posts = [];
  bool _isLoading = false;
  bool _showAppBarTitle = false;
  bool _isSearching = false;
  List<Map<String, dynamic>> _searchResults = [];

  // Stream subscriptions
  StreamSubscription<Map<String, dynamic>>? _postUpdateSubscription;

  // Validation states
  bool _hasPostContentError = false;
  String _postContentErrorMessage = '';
  bool _hasPollOptionsError = false;
  String _pollOptionsErrorMessage = '';
  final bool _hasCodeContentError = false;
  final String _codeContentErrorMessage = '';

  // Define vibrant gradient colors
  final List<List<Color>> gradients = [
    [const Color(0xFF6A11CB), const Color(0xFF2575FC)], // Purple to Blue
    [const Color(0xFFFF416C), const Color(0xFFFF4B2B)], // Pink to Orange
    [const Color(0xFF00C9FF), const Color(0xFF92FE9D)], // Blue to Green
    [const Color(0xFFFF8008), const Color(0xFFFFC837)], // Orange to Yellow
  ];



  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this); // Changed from 4 to 2 tabs
    _scrollController.addListener(_onScroll);

    // Initialize challenges scroll controller
    _challengesScrollController = ScrollController();
    _challengesScrollController?.addListener(_onChallengesScroll);

    // Listen to scroll events to animate the app bar
    _scrollController.addListener(() {
      setState(() {
        _showAppBarTitle = _scrollController.offset > 100;
      });
    });

    // Refresh notification count
    if (_auth.currentUser != null) {
      NotificationService().refreshUnreadNotificationCount(_auth.currentUser!.uid);
    }

    // Load posts from Firebase when the page loads
    _loadPosts();

    // Setup post update listener
    _setupPostUpdateListener();

    // If a specific post ID is provided, load and scroll to it after loading
    if (widget.postId != null) {
      Future.delayed(const Duration(seconds: 2), () {
        _loadAndScrollToPost(widget.postId!);
      });
    }
  }

  // Setup listener for post updates from other pages
  void _setupPostUpdateListener() {
    _postUpdateSubscription = _postUpdateService.postUpdates.listen((updateData) {
      final String postId = updateData['postId'];
      final Map<String, dynamic> updates = updateData['updates'];

      // Find the post in the current list and update it
      final postIndex = _posts.indexWhere((post) => post['id'] == postId);
      if (postIndex != -1 && mounted) {
        setState(() {
          _posts[postIndex].addAll(updates);
        });
        debugPrint('CommunityPage: Updated post $postId with $updates');
      }
    });
  }

  // Load and scroll to a specific post by ID (for deeplinks)
  Future<void> _loadAndScrollToPost(String postId) async {
    debugPrint('Looking for post with ID: $postId');

    // First check if the post is already loaded
    final postIndex = _posts.indexWhere((post) => post['id'] == postId);
    if (postIndex != -1) {
      debugPrint('Post found in loaded posts at index: $postIndex');
      _scrollToPostAtIndex(postIndex);
      return;
    }

    // If not found in loaded posts, try to fetch it from Firebase
    try {
      debugPrint('Post not found in loaded posts, fetching from Firebase...');
      final postDoc = await FirebaseFirestore.instance
          .collection('posts')
          .doc(postId)
          .get();

      if (postDoc.exists) {
        debugPrint('Post found in Firebase, loading it...');
        // Load the specific post and add it to the beginning of the list
        final data = postDoc.data() as Map<String, dynamic>;

        if (!mounted) return;
        final userContentService = Provider.of<UserContentService>(context, listen: false);

        // Create post map similar to _loadPosts method
        final Timestamp timestamp = data['createdAt'] as Timestamp? ?? Timestamp.now();
        final DateTime postDate = timestamp.toDate();
        final String timeAgo = _getTimeAgo(postDate);
        final bool hasImage = data['imageUrl'] != null;
        final String postType = data['type'] as String? ?? 'regular';

        String authorName = data['authorName'] ?? data['author'] ?? 'Unknown';
        String authorImage = data['authorImage'] ?? data['authorAvatar'] ?? '';
        String authorId = data['authorId'] ?? data['userId'] ?? '';

        // Fetch author info if needed
        if ((authorName == 'Unknown' || authorImage.isEmpty) && authorId.isNotEmpty) {
          try {
            final userDoc = await FirebaseFirestore.instance
                .collection('users')
                .doc(authorId)
                .get();

            if (userDoc.exists) {
              final userData = userDoc.data() as Map<String, dynamic>;
              authorName = userData['displayName'] ?? userData['username'] ?? 'Unknown';
              authorImage = userData['photoURL'] ?? userData['profileImageUrl'] ?? '';
            }
          } catch (e) {
            debugPrint('Error fetching author data: $e');
          }
        }

        final Map<String, dynamic> post = {
          'id': postDoc.id,
          'author': authorName,
          'authorImage': authorImage,
          'authorId': authorId,
          'badge': 'Member',
          'badgeColor': Colors.blue,
          'time': timeAgo,
          'content': data['content'] ?? '',
          'hasImage': hasImage,
          'imageUrl': data['imageUrl'],
          'likes': data['likesCount'] ?? 0,
          'comments': data['commentsCount'] ?? 0,
          'isLiked': false,
          'isSaved': false,
          'type': postType,
        };

        // Add type-specific data
        if (postType == 'poll') {
          final List<dynamic> rawOptions = data['pollOptions'] ?? [];
          post['pollOptions'] = rawOptions;

          final Timestamp? pollEndTimestamp = data['pollEndDate'] as Timestamp?;
          if (pollEndTimestamp != null) {
            final DateTime pollEndDate = pollEndTimestamp.toDate();
            final Duration timeLeft = pollEndDate.difference(DateTime.now());

            if (timeLeft.isNegative) {
              post['pollTimeLeft'] = 'Poll ended';
            } else if (timeLeft.inDays > 0) {
              post['pollTimeLeft'] = '${timeLeft.inDays} days left';
            } else if (timeLeft.inHours > 0) {
              post['pollTimeLeft'] = '${timeLeft.inHours} hours left';
            } else {
              post['pollTimeLeft'] = '${timeLeft.inMinutes} minutes left';
            }
          } else {
            post['pollTimeLeft'] = 'Poll active';
          }
        } else if (postType == 'code') {
          post['codeLanguage'] = data['codeLanguage'] ?? 'Code';
          post['code'] = data['code'] ?? '';
        } else if (postType == 'question') {
          post['questionCategory'] = data['questionCategory'] ?? '';
          post['questionTitle'] = data['questionTitle'] ?? '';
          post['answers'] = data['answers'] ?? 0;
          post['views'] = data['views'] ?? 0;
        } else if (postType == 'event') {
          post['eventTitle'] = data['eventTitle'] ?? '';
          post['eventDate'] = data['eventDate'] ?? '';
          post['eventTime'] = data['eventTime'] ?? '';
          post['eventLocation'] = data['eventLocation'] ?? '';
          post['eventAddress'] = data['eventAddress'] ?? '';
          post['attendeeCount'] = data['attendeeCount'] ?? 0;
          post['eventImage'] = data['eventImage'] ?? '';
        }

        // Check if user liked and saved the post
        if (_auth.currentUser != null) {
          post['isLiked'] = await _performanceService.isPostLiked(postDoc.id);
          post['isSaved'] = await userContentService.isPostSaved(postDoc.id);
        }

        // Add the post to the beginning of the list and scroll to it
        if (mounted) {
          setState(() {
            _posts.insert(0, post);
          });

          // Scroll to the top (where we inserted the post)
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 800),
            curve: Curves.easeInOut,
          );

          _showSnackBar('Found post!', backgroundColor: gradients[0][0]);
        }
      } else {
        debugPrint('Post not found in Firebase');
        _showSnackBar('Post not found', backgroundColor: Colors.orange);
      }
    } catch (e) {
      debugPrint('Error loading specific post: $e');
      _showSnackBar('Error loading post', backgroundColor: Colors.red);
    }
  }

  // Scroll to a specific post by index
  void _scrollToPostAtIndex(int postIndex) {
    // Calculate approximate position (each post is roughly 400 pixels)
    final double position = postIndex * 400.0;
    _scrollController.animateTo(
      position,
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeInOut,
    );

    // Show a brief highlight or message
    _showSnackBar('Found post!', backgroundColor: gradients[0][0]);
  }

  // Load posts from Firebase
  Future<void> _loadPosts() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Get the UserContentService
      final userContentService = Provider.of<UserContentService>(context, listen: false);

      // Get posts from Firebase
      final QuerySnapshot postsSnapshot = await FirebaseFirestore.instance
          .collection('posts')
          .orderBy('createdAt', descending: true)
          .limit(10)
          .get();

      final List<Map<String, dynamic>> loadedPosts = [];

      for (final doc in postsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final Timestamp timestamp = data['createdAt'] as Timestamp? ?? Timestamp.now();
        final DateTime postDate = timestamp.toDate();
        final String timeAgo = _getTimeAgo(postDate);

        // Check if post has image
        final bool hasImage = data['imageUrl'] != null;

        // Determine post type
        final String postType = data['type'] as String? ?? 'regular';

        // Get author information - first try from post data, then fetch from users collection
        String authorName = data['authorName'] ?? data['author'] ?? 'Unknown';
        String authorImage = data['authorImage'] ?? data['authorAvatar'] ?? '';
        String authorId = data['authorId'] ?? data['userId'] ?? '';

        // If author info is not in post data or is 'Unknown', fetch from users collection
        if ((authorName == 'Unknown' || authorImage.isEmpty) && authorId.isNotEmpty) {
          try {
            final userDoc = await FirebaseFirestore.instance
                .collection('users')
                .doc(authorId)
                .get();

            if (userDoc.exists) {
              final userData = userDoc.data() as Map<String, dynamic>;
              authorName = userData['displayName'] ?? userData['username'] ?? 'Unknown';
              authorImage = userData['photoURL'] ?? userData['profileImageUrl'] ?? '';
              debugPrint('Fetched author info: $authorName, image: $authorImage');
            }
          } catch (e) {
            debugPrint('Error fetching user data for post: $e');
          }
        }

        // Create post map
        final Map<String, dynamic> post = {
          'id': doc.id,
          'author': authorName,
          'authorImage': authorImage,
          'authorId': authorId,
          'badge': 'Member',
          'badgeColor': Colors.blue,
          'time': timeAgo,
          'content': data['content'] ?? '',
          'hasImage': hasImage,
          'imageUrl': data['imageUrl'],
          'likes': data['likesCount'] ?? 0,
          'comments': data['commentsCount'] ?? 0,
          'isLiked': false, // Will be updated when we check if user liked the post
          'isSaved': false, // Will be updated when we check if user saved the post
          'type': postType,
        };

        // Add type-specific data
        if (postType == 'poll') {
          // Safely cast poll options to ensure proper type
          final List<dynamic> rawOptions = data['pollOptions'] ?? [];
          post['pollOptions'] = rawOptions;

          // Calculate time left for poll
          final Timestamp? pollEndTimestamp = data['pollEndDate'] as Timestamp?;
          if (pollEndTimestamp != null) {
            final DateTime pollEndDate = pollEndTimestamp.toDate();
            final Duration timeLeft = pollEndDate.difference(DateTime.now());

            if (timeLeft.isNegative) {
              post['pollTimeLeft'] = 'Poll ended';
            } else if (timeLeft.inDays > 0) {
              post['pollTimeLeft'] = '${timeLeft.inDays} days left';
            } else if (timeLeft.inHours > 0) {
              post['pollTimeLeft'] = '${timeLeft.inHours} hours left';
            } else {
              post['pollTimeLeft'] = '${timeLeft.inMinutes} minutes left';
            }
          } else {
            post['pollTimeLeft'] = 'Poll active';
          }
        } else if (postType == 'code') {
          post['codeLanguage'] = data['codeLanguage'] ?? 'Code';
          post['code'] = data['code'] ?? '';
        } else if (postType == 'question') {
          post['questionCategory'] = data['questionCategory'] ?? '';
          post['questionTitle'] = data['questionTitle'] ?? '';
          post['answers'] = data['answers'] ?? 0;
          post['views'] = data['views'] ?? 0;
        } else if (postType == 'event') {
          post['eventTitle'] = data['eventTitle'] ?? '';
          post['eventDate'] = data['eventDate'] ?? '';
          post['eventTime'] = data['eventTime'] ?? '';
          post['eventLocation'] = data['eventLocation'] ?? '';
          post['eventAddress'] = data['eventAddress'] ?? '';
          post['attendeeCount'] = data['attendeeCount'] ?? 0;
          post['eventImage'] = data['eventImage'] ?? '';
        }

        loadedPosts.add(post);
      }

      // Update UI with posts first (without liked/saved status for faster loading)
      if (mounted) {
        setState(() {
          _posts.clear();
          _posts.addAll(loadedPosts);
          _isLoading = false;
        });
      }

      // Update liked/saved status in background for better performance
      if (_auth.currentUser != null && loadedPosts.isNotEmpty) {
        _updatePostStatusInBackground(userContentService, loadedPosts);
      }
    } catch (e) {
      debugPrint('Error loading posts: $e');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Update post liked/saved status in background for better performance
  Future<void> _updatePostStatusInBackground(UserContentService userContentService, List<Map<String, dynamic>> posts) async {
    try {
      // Create lists of futures for batch processing
      final List<Future<bool>> likedFutures = [];
      final List<Future<bool>> savedFutures = [];
      final List<String> postIds = [];

      for (final post in posts) {
        final postId = post['id'] as String;
        postIds.add(postId);
        likedFutures.add(_performanceService.isPostLiked(postId));
        savedFutures.add(userContentService.isPostSaved(postId));
      }

      // Wait for all status checks to complete
      final List<bool> likedResults = await Future.wait(likedFutures);
      final List<bool> savedResults = await Future.wait(savedFutures);

      // Update the posts with the results
      if (mounted) {
        setState(() {
          for (int i = 0; i < postIds.length && i < _posts.length; i++) {
            final postIndex = _posts.indexWhere((post) => post['id'] == postIds[i]);
            if (postIndex != -1) {
              _posts[postIndex]['isLiked'] = likedResults[i];
              _posts[postIndex]['isSaved'] = savedResults[i];
            }
          }
        });
      }
    } catch (e) {
      debugPrint('Error updating post status in background: $e');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    _challengesScrollController?.dispose();
    _postController.dispose();
    _commentController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    _searchDebounceTimer?.cancel();
    _postUpdateSubscription?.cancel();
    super.dispose();
  }

  // Handle challenges scroll to hide/show FAB
  void _onChallengesScroll() {
    if (_challengesScrollController == null) return;

    // Hide FAB when scrolling down to create challenge section (near bottom)
    final maxScroll = _challengesScrollController!.position.maxScrollExtent;
    final currentScroll = _challengesScrollController!.position.pixels;
    final threshold = maxScroll * 0.7; // Hide when 70% scrolled

    final shouldShowFab = currentScroll < threshold;

    if (shouldShowFab != _showFab) {
      setState(() {
        _showFab = shouldShowFab;
      });
    }
  }

  // Search for users with debounce
  Timer? _searchDebounceTimer;

  // Debounce mechanism for likes
  final Set<String> _likingPosts = <String>{};
  final Set<String> _likingComments = <String>{};

  // Show full-screen search page
  void _showSearchDialog() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SearchPage(),
      ),
    );
  }





  // Legacy search method - keeping for reference
  Future<void> _searchUsers(String query) async {
    // This method is no longer used directly
    // We're using the dialog-based search instead
  }

  // Debounce mechanism to prevent multiple scroll events
  DateTime _lastLoadTime = DateTime.now();

  void _onScroll() {
    // Trigger loading when we're within 800 pixels of the bottom
    // This gives more time to load content before reaching the end
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 800) {
      // Only load more if at least 2 seconds has passed since last load
      // This prevents rapid firing of load events during fast scrolling
      if (DateTime.now().difference(_lastLoadTime).inMilliseconds > 2000) {
        _loadMoreData();
        _lastLoadTime = DateTime.now();
      }
    }
  }

  Future<void> _loadMoreData() async {
    if (!_isLoading) {
      // Set loading state without triggering a full rebuild
      if (mounted) {
        setState(() {
          _isLoading = true;
        });
      }

      try {
        // Get the UserContentService
        final userContentService = Provider.of<UserContentService>(context, listen: false);

        // For pagination, use the last post's timestamp for efficient pagination
        Timestamp? lastPostTimestamp;
        if (_posts.isNotEmpty) {
          // Find the last post's document ID
          final lastPostId = _posts.last['id'];

          // Get the last post's document to get its timestamp
          final lastPostDoc = await FirebaseFirestore.instance
              .collection('posts')
              .doc(lastPostId)
              .get();

          if (lastPostDoc.exists) {
            final lastPostData = lastPostDoc.data() as Map<String, dynamic>;
            lastPostTimestamp = lastPostData['createdAt'] as Timestamp?;
          }
        }

        // Query for more posts using the timestamp for pagination
        Query postsQuery = FirebaseFirestore.instance
            .collection('posts')
            .orderBy('createdAt', descending: true)
            .limit(5);

        // If we have a timestamp from the last post, use it for pagination
        if (lastPostTimestamp != null) {
          postsQuery = postsQuery.startAfter([lastPostTimestamp]);
        }

        final QuerySnapshot postsSnapshot = await postsQuery.get();

        final List<Map<String, dynamic>> newPosts = [];

        for (final doc in postsSnapshot.docs) {
          final data = doc.data() as Map<String, dynamic>;
          final Timestamp timestamp = data['createdAt'] as Timestamp? ?? Timestamp.now();
          final DateTime postDate = timestamp.toDate();
          final String timeAgo = _getTimeAgo(postDate);

          // Check if post is already in the list
          if (_posts.any((post) => post['id'] == doc.id)) {
            continue;
          }

          // Check if post has image
          final bool hasImage = data['imageUrl'] != null;

          // Determine post type
          final String postType = data['type'] as String? ?? 'regular';

          // Get author information - first try from post data, then fetch from users collection
          String authorName = data['authorName'] ?? data['author'] ?? 'Unknown';
          String authorImage = data['authorImage'] ?? data['authorAvatar'] ?? '';
          String authorId = data['authorId'] ?? data['userId'] ?? '';

          // If author info is not in post data or is 'Unknown', fetch from users collection
          if ((authorName == 'Unknown' || authorImage.isEmpty) && authorId.isNotEmpty) {
            try {
              final userDoc = await FirebaseFirestore.instance
                  .collection('users')
                  .doc(authorId)
                  .get();

              if (userDoc.exists) {
                final userData = userDoc.data() as Map<String, dynamic>;
                authorName = userData['displayName'] ?? userData['username'] ?? 'Unknown';
                authorImage = userData['photoURL'] ?? userData['profileImageUrl'] ?? '';
                debugPrint('Fetched author info for pagination: $authorName, image: $authorImage');
              }
            } catch (e) {
              debugPrint('Error fetching user data for post: $e');
            }
          }

          // Create post map
          final Map<String, dynamic> post = {
            'id': doc.id,
            'author': authorName,
            'authorImage': authorImage,
            'authorId': authorId,
            'badge': 'Member',
            'badgeColor': Colors.blue,
            'time': timeAgo,
            'content': data['content'] ?? '',
            'hasImage': hasImage,
            'imageUrl': data['imageUrl'],
            'likes': data['likesCount'] ?? 0,
            'comments': data['commentsCount'] ?? 0,
            'isLiked': false, // Will be updated when we check if user liked the post
            'isSaved': false, // Will be updated when we check if user saved the post
            'type': postType,
          };

          // Add type-specific data
          if (postType == 'poll') {
            // Safely cast poll options to ensure proper type
            final List<dynamic> rawOptions = data['pollOptions'] ?? [];
            post['pollOptions'] = rawOptions;

            // Calculate time left for poll
            final Timestamp? pollEndTimestamp = data['pollEndDate'] as Timestamp?;
            if (pollEndTimestamp != null) {
              final DateTime pollEndDate = pollEndTimestamp.toDate();
              final Duration timeLeft = pollEndDate.difference(DateTime.now());

              if (timeLeft.isNegative) {
                post['pollTimeLeft'] = 'Poll ended';
              } else if (timeLeft.inDays > 0) {
                post['pollTimeLeft'] = '${timeLeft.inDays} days left';
              } else if (timeLeft.inHours > 0) {
                post['pollTimeLeft'] = '${timeLeft.inHours} hours left';
              } else {
                post['pollTimeLeft'] = '${timeLeft.inMinutes} minutes left';
              }
            } else {
              post['pollTimeLeft'] = 'Poll active';
            }
          } else if (postType == 'code') {
            post['codeLanguage'] = data['codeLanguage'] ?? 'Code';
            post['code'] = data['code'] ?? '';
          } else if (postType == 'question') {
            post['questionCategory'] = data['questionCategory'] ?? '';
            post['questionTitle'] = data['questionTitle'] ?? '';
            post['answers'] = data['answers'] ?? 0;
            post['views'] = data['views'] ?? 0;
          } else if (postType == 'event') {
            post['eventTitle'] = data['eventTitle'] ?? '';
            post['eventDate'] = data['eventDate'] ?? '';
            post['eventTime'] = data['eventTime'] ?? '';
            post['eventLocation'] = data['eventLocation'] ?? '';
            post['eventAddress'] = data['eventAddress'] ?? '';
            post['attendeeCount'] = data['attendeeCount'] ?? 0;
            post['eventImage'] = data['eventImage'] ?? '';
          }

          newPosts.add(post);
        }

        if (mounted) {
          setState(() {
            _posts.addAll(newPosts);
            _isLoading = false;
          });
        }

        // Update liked/saved status in background for better performance
        if (_auth.currentUser != null && newPosts.isNotEmpty) {
          _updatePostStatusInBackground(userContentService, newPosts);
        }
      } catch (e) {
        debugPrint('Error loading more posts: $e');

        // Update state to stop loading
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  // Helper method to format time ago
  String _getTimeAgo(DateTime dateTime) {
    final Duration difference = DateTime.now().difference(dateTime);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} years ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} months ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  // Helper method to show snackbar safely
  void _showSnackBar(String message, {Color backgroundColor = Colors.green}) {
    if (!mounted) return;

    // Use a post-frame callback to avoid BuildContext across async gap warnings
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            message,
            style: GoogleFonts.poppins(),
          ),
          backgroundColor: backgroundColor,
          behavior: SnackBarBehavior.floating,
        ),
      );
    });
  }

  // Safely pop dialog to prevent Navigator context errors
  void _safelyPopDialog() {
    try {
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('Error popping dialog: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA), // Light background
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        title: _isSearching
            ? TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                decoration: InputDecoration(
                  hintText: 'Search users...',
                  border: InputBorder.none,
                  hintStyle: GoogleFonts.poppins(
                    color: Colors.grey[400],
                  ),
                  // Prevent text field from losing focus
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: Colors.grey),
                          onPressed: () {
                            setState(() {
                              _searchController.clear();
                              _searchResults = [];
                            });
                            // Refocus after clearing
                            _searchFocusNode.requestFocus();
                          },
                        )
                      : null,
                ),
                onChanged: (value) {
                  _searchUsers(value);
                },
                autofocus: true,
                // Prevent keyboard from closing when tapping outside
                onTap: () {
                  // Keep focus on the text field
                  _searchFocusNode.requestFocus();
                },
              )
            : AnimatedOpacity(
                opacity: _showAppBarTitle ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 200),
                child: Text(
                  'Community',
                  style: GoogleFonts.poppins(
                    color: const Color(0xFF333333),
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
              ),
        leading: _isSearching
            ? IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black87),
                onPressed: () {
                  // Cancel any pending search
                  _searchDebounceTimer?.cancel();

                  // Clear search state
                  setState(() {
                    _isSearching = false;
                    _searchController.clear();
                    _searchResults = [];
                  });
                },
              )
            : null,
        actions: [
          // Search icon
          IconButton(
            icon: Icon(Icons.search, color: Colors.grey[800]),
            onPressed: () {
              // Show search dialog
              _showSearchDialog();
            },
          ),
          // Message icon added to top bar with dynamic unread count
          StreamBuilder<int>(
            stream: MessagingService().getTotalUnreadMessageCount(),
            builder: (context, snapshot) {
              final int unreadCount = snapshot.data ?? 0;

              return IconButton(
                icon: Stack(
                  children: [
                    Icon(Icons.message_rounded, color: Colors.grey[800]),
                    if (unreadCount > 0)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          padding: const EdgeInsets.all(1),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 12,
                            minHeight: 12,
                          ),
                          child: Text(
                            unreadCount.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 8,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const MessagingPage(),
                    ),
                  );
                },
              );
            },
          ),
          // Notification icon with dynamic unread count
          StreamBuilder<int>(
            stream: NotificationService().getUnreadNotificationCount(_auth.currentUser?.uid ?? ''),
            builder: (context, snapshot) {
              final int unreadCount = snapshot.data ?? 0;

              return IconButton(
                icon: Stack(
                  children: [
                    Icon(Icons.notifications_outlined, color: Colors.grey[800]),
                    if (unreadCount > 0)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          padding: const EdgeInsets.all(1),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 12,
                            minHeight: 12,
                          ),
                          child: Text(
                            unreadCount.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 8,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const NotificationsPage(),
                    ),
                  );
                },
              );
            },
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: GestureDetector(
              onTap: () {},
              child: Hero(
                tag: 'community-profile-avatar',
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 4,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      width: 32,
                      height: 32,
                      color: Colors.grey[300],
                      child: Icon(
                        Icons.person,
                        size: 20,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(50),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(5),
                  blurRadius: 4,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: Container(
                height: 40,
                width: 200, // Fixed width to center the tabs
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(25),
                ),
                child: TabBar(
                  controller: _tabController,
                  isScrollable: false, // Changed to false since we only have 2 tabs
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                    gradient: LinearGradient(
                      colors: gradients[0],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: gradients[0][0].withAlpha(50),
                        blurRadius: 4,
                        spreadRadius: 0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.grey[700],
                  labelStyle: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                  unselectedLabelStyle: GoogleFonts.poppins(
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  dividerColor: Colors.transparent,
                  splashBorderRadius: BorderRadius.circular(25),
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  labelPadding: const EdgeInsets.symmetric(horizontal: 12),
                  tabs: const [
                    Tab(text: 'Activity'),
                    // Tab(text: 'Forums'), // Disabled for future
                    // Tab(text: 'Mentorship'), // Disabled for future
                    Tab(text: 'Challenges'),
                  ],
                ).animate().fadeIn(duration: 500.ms).scale(begin: const Offset(0.95, 0.95), duration: 500.ms),
              ),
            ),
          ),
        ),
      ),
      body: TabBarView(
              controller: _tabController,
              children: [
                _buildActivityFeed(),
                // _buildForumsAndGroups(), // Disabled for future
                // _buildMentorship(), // Disabled for future
                _buildContests(),
              ],
            ),
      floatingActionButton: _tabController.index == 0
        ? FloatingActionButton(
            onPressed: () {
              _showCreatePostModal(context);
            },
            backgroundColor: Colors.transparent,
            elevation: 8,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: gradients[1],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: gradients[1][0].withAlpha(100),
                    blurRadius: 12,
                    spreadRadius: 0,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(Icons.add, color: Colors.white, size: 28),
            ),
          )
        : _tabController.index == 1 && _showFab
          ? FloatingActionButton.extended(
              onPressed: () {
                _showCreateChallengeModal(context);
              },
              icon: const Icon(Icons.emoji_events, size: 18),
              label: Text(
                'Create Challenge',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
              ),
              backgroundColor: const Color(0xFF4F46E5),
              foregroundColor: Colors.white,
              elevation: 4,
              extendedPadding: const EdgeInsets.symmetric(horizontal: 16),
            )
          : null,
    );
  }

  // Show create challenge modal
  void _showCreateChallengeModal(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CreateChallengeDialog(
        challengeService: _challengeService,
      ),
    );
  }

  // Add transaction to history (for profile transaction history)
  Future<void> _addTransactionToHistory({
    required String userId,
    required String type,
    required double amount,
    required String description,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      await FirebaseFirestore.instance.collection('transactions').add({
        'userId': userId,
        'type': type, // 'challenge_creation', 'challenge_win', 'refund', 'challenge_fee'
        'amount': amount,
        'description': description,
        'metadata': metadata ?? {},
        'createdAt': FieldValue.serverTimestamp(),
        'status': 'completed',
      });
    } catch (e) {
      debugPrint('Error adding transaction to history: $e');
    }
  }

  void _showCreatePostModal(BuildContext context) {
    // We'll get the UserContentService when needed

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (BuildContext context, StateSetter setModalState) => Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(20),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              margin: const EdgeInsets.only(top: 10),
              width: 40,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ShaderMask(
                    shaderCallback: (bounds) => LinearGradient(
                      colors: gradients[1],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ).createShader(bounds),
                    child: Text(
                      'Create Post',
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: Icon(Icons.close, color: Colors.grey[800]),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                ],
              ),
            ),
            Divider(color: Colors.grey[200]),
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Row(
                children: [
                  StreamBuilder<User?>(
                    stream: FirebaseAuth.instance.authStateChanges(),
                    builder: (context, snapshot) {
                      final user = snapshot.data;
                      return Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: gradients[1],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: gradients[1][0].withAlpha(50),
                              blurRadius: 8,
                              spreadRadius: 0,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        padding: const EdgeInsets.all(2),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: user?.photoURL != null
                              ? Image.network(
                                  user!.photoURL!,
                                  width: 44,
                                  height: 44,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      width: 44,
                                      height: 44,
                                      color: Colors.grey[300],
                                      child: Icon(
                                        Icons.person,
                                        size: 24,
                                        color: Colors.grey[600],
                                      ),
                                    );
                                  },
                                )
                              : Container(
                                  width: 44,
                                  height: 44,
                                  color: Colors.grey[300],
                                  child: Icon(
                                    Icons.person,
                                    size: 24,
                                    color: Colors.grey[600],
                                  ),
                                ),
                        ),
                      );
                    }
                  ),
                  const SizedBox(width: 16),
                  StreamBuilder<User?>(
                    stream: FirebaseAuth.instance.authStateChanges(),
                    builder: (context, snapshot) {
                      final user = snapshot.data;
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            user?.displayName ?? 'User',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                              fontSize: 16,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: gradients[1],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'Member',
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      );
                    }
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.public,
                          color: Colors.grey[700],
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'Public',
                          style: GoogleFonts.poppins(
                            color: Colors.grey[700],
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 2),
                        Icon(
                          Icons.keyboard_arrow_down,
                          color: Colors.grey[700],
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 20),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: _hasPostContentError ? Colors.red[400]! : Colors.grey[300]!,
                              width: 1,
                            ),
                          ),
                          padding: const EdgeInsets.all(16),
                          child: TextField(
                            controller: _postController,
                            // Make text field larger when no content type is selected
                            maxLines: !_isPhotoMode && !_isCodeMode && !_isPollMode ? 8 : 3,
                            minLines: !_isPhotoMode && !_isCodeMode && !_isPollMode ? 5 : 1,
                            style: GoogleFonts.poppins(
                              color: Colors.black87,
                              fontSize: !_isPhotoMode && !_isCodeMode && !_isPollMode ? 16 : 14,
                            ),
                            decoration: InputDecoration(
                              hintText: !_isPhotoMode && !_isCodeMode && !_isPollMode
                                  ? 'Share your thoughts with the community...'
                                  : 'Add a description...',
                              hintStyle: GoogleFonts.poppins(
                                color: Colors.grey[500],
                                fontSize: !_isPhotoMode && !_isCodeMode && !_isPollMode ? 16 : 14,
                              ),
                              border: InputBorder.none,
                            ),
                            onChanged: (value) {
                              // Clear error when user starts typing
                              if (_hasPostContentError && value.trim().isNotEmpty) {
                                setModalState(() {
                                  _hasPostContentError = false;
                                  _postContentErrorMessage = '';
                                });
                              }
                            },
                          ),
                        ),
                        // Error message
                        if (_hasPostContentError && _postContentErrorMessage.isNotEmpty)
                          Container(
                            margin: const EdgeInsets.only(left: 20, right: 20, top: 8),
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              color: Colors.red[50],
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.red[200]!, width: 1),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  color: Colors.red[600],
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _postContentErrorMessage,
                                    style: GoogleFonts.poppins(
                                      color: Colors.red[700],
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),

                    // Selected image preview
                    if (_selectedImage != null && _isPhotoMode)
                      Container(
                        margin: const EdgeInsets.fromLTRB(20, 16, 20, 0),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.grey[300]!,
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(20),
                              blurRadius: 6,
                              spreadRadius: 0,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            // Image header
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: gradients[0][0].withAlpha(20),
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(16),
                                  topRight: Radius.circular(16),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: gradients[0][0],
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(
                                          Icons.image,
                                          color: Colors.white,
                                          size: 14,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          'Image',
                                          style: GoogleFonts.poppins(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const Spacer(),
                                  Row(
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          _pickImageForModal(setModalState);
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            shape: BoxShape.circle,
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black.withAlpha(10),
                                                blurRadius: 2,
                                                spreadRadius: 0,
                                                offset: const Offset(0, 1),
                                              ),
                                            ],
                                          ),
                                          child: Icon(
                                            Icons.edit,
                                            size: 14,
                                            color: gradients[0][0],
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      GestureDetector(
                                        onTap: () {
                                          setModalState(() {
                                            _selectedImage = null;
                                            _isPhotoMode = false;
                                          });
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            shape: BoxShape.circle,
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black.withAlpha(10),
                                                blurRadius: 2,
                                                spreadRadius: 0,
                                                offset: const Offset(0, 1),
                                              ),
                                            ],
                                          ),
                                          child: const Icon(
                                            Icons.close,
                                            size: 14,
                                            color: Colors.red,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),

                            // Image content
                            Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: const BorderRadius.only(
                                    bottomLeft: Radius.circular(16),
                                    bottomRight: Radius.circular(16),
                                  ),
                                  child: Image.file(
                                    _selectedImage!,
                                    width: double.infinity,
                                    height: 200,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                    // Code snippet preview
                    if (_selectedCodeLanguage != null && _codeContent != null && _isCodeMode)
                      Container(
                        margin: const EdgeInsets.fromLTRB(20, 16, 20, 0),
                        decoration: BoxDecoration(
                          color: const Color(0xFF1E1E1E),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.grey[800]!,
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(30),
                              blurRadius: 6,
                              spreadRadius: 0,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Code header
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.black.withAlpha(100),
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(16),
                                  topRight: Radius.circular(16),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: gradients[3][0],
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(
                                          Icons.code,
                                          color: Colors.white,
                                          size: 14,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          _selectedCodeLanguage!,
                                          style: GoogleFonts.poppins(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const Spacer(),
                                  Row(
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          _showCodeInputDialog();
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: BoxDecoration(
                                            color: Colors.white.withAlpha(30),
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(
                                            Icons.edit,
                                            color: Colors.white,
                                            size: 14,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      GestureDetector(
                                        onTap: () {
                                          setModalState(() {
                                            _selectedCodeLanguage = null;
                                            _codeContent = null;
                                            _isCodeMode = false;
                                          });
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: BoxDecoration(
                                            color: Colors.white.withAlpha(30),
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(
                                            Icons.close,
                                            color: Colors.white,
                                            size: 14,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),

                            // Code content
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: const BoxDecoration(
                                color: Color(0xFF1E1E1E),
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(16),
                                  bottomRight: Radius.circular(16),
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Line numbers and code content
                                  SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        // Line numbers
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.end,
                                          children: _generateLineNumbers(_codeContent!),
                                        ),
                                        const SizedBox(width: 12),
                                        // Code content
                                        SizedBox(
                                          width: MediaQuery.of(context).size.width * 0.7,
                                          child: Text(
                                            _codeContent!,
                                            style: GoogleFonts.firaCode(
                                              color: Colors.white,
                                              fontSize: 12,
                                              height: 1.5,
                                            ),
                                            maxLines: 10,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                    // Poll options preview
                    if (_pollOptions != null && _isPollMode)
                      Container(
                        margin: const EdgeInsets.fromLTRB(20, 16, 20, 0),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.grey[300]!,
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(13),
                              blurRadius: 4,
                              spreadRadius: 0,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Poll header
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: gradients[2][0].withAlpha(20),
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(16),
                                  topRight: Radius.circular(16),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: gradients[2][0],
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(
                                          Icons.poll,
                                          color: Colors.white,
                                          size: 14,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          'Poll',
                                          style: GoogleFonts.poppins(
                                            fontWeight: FontWeight.w600,
                                            fontSize: 12,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    '${_pollOptions!.length} options',
                                    style: GoogleFonts.poppins(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 12,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                  const Spacer(),
                                  Row(
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          _showPollCreationDialog();
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            shape: BoxShape.circle,
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black.withAlpha(10),
                                                blurRadius: 2,
                                                spreadRadius: 0,
                                                offset: const Offset(0, 1),
                                              ),
                                            ],
                                          ),
                                          child: Icon(
                                            Icons.edit,
                                            size: 14,
                                            color: gradients[2][0],
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      GestureDetector(
                                        onTap: () {
                                          setModalState(() {
                                            _pollOptions = null;
                                            _isPollMode = false;
                                          });
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.all(6),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            shape: BoxShape.circle,
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black.withAlpha(10),
                                                blurRadius: 2,
                                                spreadRadius: 0,
                                                offset: const Offset(0, 1),
                                              ),
                                            ],
                                          ),
                                          child: const Icon(
                                            Icons.close,
                                            size: 14,
                                            color: Colors.red,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),

                            // Poll options
                            Padding(
                              padding: const EdgeInsets.all(12),
                              child: Column(
                                children: [
                                  for (int i = 0; i < _pollOptions!.length; i++)
                                    Padding(
                                      padding: const EdgeInsets.only(bottom: 8),
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                                        decoration: BoxDecoration(
                                          color: Colors.grey[100],
                                          borderRadius: BorderRadius.circular(12),
                                          border: Border.all(
                                            color: Colors.grey[300]!,
                                            width: 1,
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            Container(
                                              width: 24,
                                              height: 24,
                                              decoration: BoxDecoration(
                                                color: gradients[2][0].withAlpha(20),
                                                shape: BoxShape.circle,
                                              ),
                                              alignment: Alignment.center,
                                              child: Text(
                                                '${i + 1}',
                                                style: GoogleFonts.poppins(
                                                  fontWeight: FontWeight.w600,
                                                  color: gradients[2][0],
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 12),
                                            Expanded(
                                              child: Text(
                                                _pollOptions![i]['text'],
                                                style: GoogleFonts.poppins(
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
            Divider(color: Colors.grey[200]),
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Add to your post',
                    style: GoogleFonts.poppins(
                      color: Colors.grey[700],
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Post action buttons in a column for better mobile layout
                  Column(
                    children: [
                      // Media buttons row
                      Row(
                        children: [
                          InkWell(
                            onTap: () {
                              // Handle photo post type
                              setModalState(() {
                                _isPhotoMode = true;
                                _isCodeMode = false;
                                _isPollMode = false;
                              });
                              _pickImageForModal(setModalState);
                            },
                            borderRadius: BorderRadius.circular(8),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(10),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: gradients[0],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: gradients[0][0].withAlpha(40),
                                          blurRadius: 8,
                                          spreadRadius: 0,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: const Icon(Icons.image, color: Colors.white, size: 18),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Photo',
                                    style: GoogleFonts.poppins(
                                      fontWeight: FontWeight.w500,
                                      color: _isPhotoMode ? gradients[0][0] : Colors.black87,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          InkWell(
                            onTap: () {
                              // Handle poll post type
                              setModalState(() {
                                _isPhotoMode = false;
                                _isCodeMode = false;
                                _isPollMode = true;
                              });
                              _showPollCreationDialog();
                            },
                            borderRadius: BorderRadius.circular(8),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(10),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: gradients[2],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: gradients[2][0].withAlpha(40),
                                          blurRadius: 8,
                                          spreadRadius: 0,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: const Icon(Icons.poll, color: Colors.white, size: 18),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Poll',
                                    style: GoogleFonts.poppins(
                                      fontWeight: FontWeight.w500,
                                      color: _isPollMode ? gradients[2][0] : Colors.black87,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          InkWell(
                            onTap: () {
                              // Handle code post type
                              setModalState(() {
                                _isPhotoMode = false;
                                _isCodeMode = true;
                                _isPollMode = false;
                              });
                              _showCodeInputDialog();
                            },
                            borderRadius: BorderRadius.circular(8),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              child: Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(10),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: gradients[3],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: gradients[3][0].withAlpha(40),
                                          blurRadius: 8,
                                          spreadRadius: 0,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: const Icon(Icons.code, color: Colors.white, size: 18),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Code',
                                    style: GoogleFonts.poppins(
                                      fontWeight: FontWeight.w500,
                                      color: _isCodeMode ? gradients[3][0] : Colors.black87,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),

                      // Post button in a separate row for better visibility
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity, // Make button full width
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: gradients[1],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(30),
                            boxShadow: [
                              BoxShadow(
                                color: gradients[1][0].withAlpha(50),
                                blurRadius: 8,
                                spreadRadius: 0,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: ElevatedButton(
                            onPressed: () async {
                              // Get the post content from the text field
                              final String postContent = _postController.text.trim();

                              // Validate post content
                              if (postContent.isEmpty) {
                                setModalState(() {
                                  _hasPostContentError = true;
                                  _postContentErrorMessage = 'Please enter some content for your post';
                                });
                                return;
                              } else {
                                setModalState(() {
                                  _hasPostContentError = false;
                                  _postContentErrorMessage = '';
                                });
                              }

                              // Show loading indicator
                              showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (BuildContext context) {
                                  return const Center(
                                    child: CircularProgressIndicator(),
                                  );
                                },
                              );

                              try {
                                // Use the UserContentService from the parent context
                                final userContentService = Provider.of<UserContentService>(context, listen: false);

                                // Determine post type and additional data
                                String postType = 'regular';
                                Map<String, dynamic> additionalData = {};

                                if (_isPhotoMode && _selectedImage != null) {
                                  postType = 'regular'; // Regular post with image
                                  // Image will be uploaded separately
                                } else if (_isCodeMode && _codeContent != null && _selectedCodeLanguage != null) {
                                  postType = 'code';
                                  additionalData = {
                                    'code': _codeContent,
                                    'codeLanguage': _selectedCodeLanguage,
                                  };
                                } else if (_isPollMode && _pollOptions != null && _pollOptions!.isNotEmpty) {
                                  postType = 'poll';
                                  additionalData = {
                                    'pollOptions': _pollOptions,
                                    'pollEndDate': Timestamp.fromDate(
                                      DateTime.now().add(Duration(days: _pollExpiryDays)),
                                    ),
                                  };
                                }

                                // Create post
                                Map<String, dynamic> result;

                                if (_isPhotoMode && _selectedImage != null) {
                                  // Create post with image
                                  result = await userContentService.createPostWithImage(
                                    content: postContent,
                                    imageFile: _selectedImage!,
                                    type: postType,
                                    additionalData: additionalData,
                                  );
                                } else {
                                  // Create post without image
                                  result = await userContentService.createPost(
                                    content: postContent,
                                    type: postType,
                                    additionalData: additionalData,
                                  );
                                }

                                // Check if still mounted before updating UI
                                if (!mounted) return;

                                // Close loading dialog
                                _safelyPopDialog();

                                // Close post creation modal
                                _safelyPopDialog();

                                if (result['success']) {
                                  // Reset post creation state
                                  setState(() {
                                    _selectedImage = null;
                                    _selectedCodeLanguage = null;
                                    _codeContent = null;
                                    _pollOptions = null;
                                    _isPhotoMode = false;
                                    _isCodeMode = false;
                                    _isPollMode = false;
                                    _postController.clear();
                                  });

                                  // Reload posts to show the new post
                                  _loadPosts();

                                  _showSnackBar('Post created successfully!', backgroundColor: gradients[1][0]);
                                } else {
                                  _showSnackBar('Failed to create post: ${result['error']}', backgroundColor: Colors.red);
                                }
                              } catch (e) {
                                debugPrint('Error creating post: $e');

                                // Check if still mounted before updating UI
                                if (!mounted) return;

                                // Close loading dialog
                                _safelyPopDialog();

                                // Close post creation modal
                                _safelyPopDialog();

                                // Reset post creation state
                                setState(() {
                                  _selectedImage = null;
                                  _selectedCodeLanguage = null;
                                  _codeContent = null;
                                  _pollOptions = null;
                                  _isPhotoMode = false;
                                  _isCodeMode = false;
                                  _isPollMode = false;
                                });

                                _showSnackBar('Error creating post: $e', backgroundColor: Colors.red);
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 15),
                              elevation: 0,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30),
                              ),
                            ),
                            child: Text(
                              'Post',
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      ),
    );
  }

  // State variables for post creation
  File? _selectedImage;
  String? _selectedCodeLanguage;
  String? _codeContent;
  List<Map<String, dynamic>>? _pollOptions;
  int _pollExpiryDays = 1;
  bool _isPhotoMode = false;
  bool _isCodeMode = false;
  bool _isPollMode = false;

  // Pick image from gallery
  Future<void> _pickImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 70, // Reduce quality for better performance
      );

      if (pickedFile != null) {
        // Pre-load the image to ensure it's available immediately
        final imageFile = File(pickedFile.path);

        // Precache the image to ensure it loads instantly
        final Uint8List imageBytes = await imageFile.readAsBytes();

        // Update UI with the selected image
        if (mounted) {
          // Precache the image in the current context
          precacheImage(MemoryImage(imageBytes), context);
          setState(() {
            _selectedImage = imageFile;
            _isPhotoMode = true;
          });

          // Show a snackbar to confirm image selection
          _showSnackBar('Image selected successfully', backgroundColor: gradients[0][0]);
        }
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
      _showSnackBar('Error selecting image: $e', backgroundColor: Colors.red);
    }
  }

  // Pick image from gallery for modal
  Future<void> _pickImageForModal(StateSetter setModalState) async {
    try {
      debugPrint('Picking image for modal...');
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 70, // Reduce quality for better performance
      );

      if (pickedFile != null) {
        debugPrint('Image picked: ${pickedFile.path}');
        // Pre-load the image to ensure it's available immediately
        final imageFile = File(pickedFile.path);

        // Precache the image to ensure it loads instantly
        final Uint8List imageBytes = await imageFile.readAsBytes();
        debugPrint('Image bytes loaded: ${imageBytes.length}');

        // Update UI with the selected image
        if (mounted) {
          // Precache the image in the current context
          precacheImage(MemoryImage(imageBytes), context);
          setModalState(() {
            _selectedImage = imageFile;
            _isPhotoMode = true;
          });
          debugPrint('Modal state updated with selected image');

          // Show a snackbar to confirm image selection
          _showSnackBar('Image selected successfully', backgroundColor: gradients[0][0]);
        }
      } else {
        debugPrint('No image selected');
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
      _showSnackBar('Error selecting image: $e', backgroundColor: Colors.red);
    }
  }

  // Show code input dialog
  void _showCodeInputDialog() {
    // Initialize with existing content if available
    final TextEditingController codeController = TextEditingController(
      text: _codeContent ?? ''
    );
    String selectedLanguage = _selectedCodeLanguage ?? 'Dart';

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(
          'Add Code Snippet',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Language selection dropdown
              DropdownButtonFormField<String>(
                value: selectedLanguage,
                decoration: InputDecoration(
                  labelText: 'Language',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                items: ['Dart', 'JavaScript', 'Python', 'Java', 'Kotlin', 'Swift', 'C#', 'HTML', 'CSS']
                    .map((language) => DropdownMenuItem(
                          value: language,
                          child: Text(language),
                        ))
                    .toList(),
                onChanged: (value) {
                  selectedLanguage = value!;
                },
              ),
              const SizedBox(height: 16),
              // Code input field
              TextField(
                controller: codeController,
                maxLines: 10,
                decoration: InputDecoration(
                  labelText: 'Code',
                  hintText: 'Paste your code here',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              // Validate code content
              if (codeController.text.trim().isEmpty) {
                // Show error in the dialog
                ScaffoldMessenger.of(dialogContext).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Please enter some code',
                      style: GoogleFonts.poppins(),
                    ),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              // Update state
              setState(() {
                _selectedCodeLanguage = selectedLanguage;
                _codeContent = codeController.text;
              });

              // Close dialog
              Navigator.pop(dialogContext);

              // Show a snackbar to confirm code addition
              _showSnackBar('Code snippet added', backgroundColor: gradients[2][0]);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: gradients[2][0],
            ),
            child: Text(
              'Add Code',
              style: GoogleFonts.poppins(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Show poll creation dialog
  void _showPollCreationDialog() {
    List<TextEditingController> optionControllers = [];
    int selectedExpiryDays = 1; // Default to 1 day

    // Initialize with existing poll options if available
    if (_pollOptions != null && _pollOptions!.isNotEmpty) {
      for (var option in _pollOptions!) {
        optionControllers.add(TextEditingController(text: option['text'] ?? ''));
      }
    } else {
      // Default empty options if none exist
      optionControllers = [
        TextEditingController(),
        TextEditingController(),
      ];
    }

    // Add a question title controller with existing content if available
    final TextEditingController questionController = TextEditingController(
      text: _isPhotoMode || _isCodeMode ? '' : _postController.text
    );

    showDialog(
      context: context,
      builder: (dialogContext) => StatefulBuilder(
        builder: (dialogContext, setDialogState) => AlertDialog(
          title: Text(
            'Create Poll',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Poll question field
                TextField(
                  controller: questionController,
                  decoration: InputDecoration(
                    labelText: 'Poll Question',
                    hintText: 'What would you like to ask?',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Poll expiry dropdown
                Row(
                  children: [
                    Text(
                      'Poll expires in:',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: DropdownButtonFormField<int>(
                        value: selectedExpiryDays,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: [
                          for (int i = 1; i <= 7; i++)
                            DropdownMenuItem<int>(
                              value: i,
                              child: Text(
                                '$i day${i > 1 ? 's' : ''}',
                                style: GoogleFonts.poppins(),
                              ),
                            ),
                        ],
                        onChanged: (value) {
                          setDialogState(() {
                            selectedExpiryDays = value ?? 1;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'Add poll options (minimum 2)',
                  style: GoogleFonts.poppins(),
                ),
                const SizedBox(height: 16),
                // Poll options
                ...List.generate(
                  optionControllers.length,
                  (index) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: optionControllers[index],
                            decoration: InputDecoration(
                              labelText: 'Option ${index + 1}',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: _hasPollOptionsError ? Colors.red[400]! : Colors.grey,
                                  width: 1,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: _hasPollOptionsError ? Colors.red[400]! : Colors.grey,
                                  width: 1,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: _hasPollOptionsError ? Colors.red[400]! : Colors.blue,
                                  width: 1,
                                ),
                              ),
                            ),
                            onChanged: (value) {
                              // Clear error when user starts typing
                              if (_hasPollOptionsError && value.trim().isNotEmpty) {
                                setDialogState(() {
                                  _hasPollOptionsError = false;
                                  _pollOptionsErrorMessage = '';
                                });
                              }
                            },
                          ),
                        ),
                        if (index >= 2)
                          IconButton(
                            icon: const Icon(Icons.remove_circle, color: Colors.red),
                            onPressed: () {
                              setDialogState(() {
                                optionControllers.removeAt(index);
                              });
                            },
                          ),
                      ],
                    ),
                  ),
                ),
                // Error message for poll options
                if (_hasPollOptionsError && _pollOptionsErrorMessage.isNotEmpty)
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.red[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red[200]!, width: 1),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red[600],
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _pollOptionsErrorMessage,
                            style: GoogleFonts.poppins(
                              color: Colors.red[700],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                // Add option button
                TextButton.icon(
                  onPressed: () {
                    setDialogState(() {
                      optionControllers.add(TextEditingController());
                    });
                  },
                  icon: const Icon(Icons.add),
                  label: Text(
                    'Add Option',
                    style: GoogleFonts.poppins(),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(dialogContext),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Validate options
                bool isValid = true;
                for (final controller in optionControllers) {
                  if (controller.text.trim().isEmpty) {
                    isValid = false;
                    break;
                  }
                }

                if (!isValid) {
                  setDialogState(() {
                    _hasPollOptionsError = true;
                    _pollOptionsErrorMessage = 'Please fill all poll options before creating the poll.';
                  });
                  return;
                } else {
                  setDialogState(() {
                    _hasPollOptionsError = false;
                    _pollOptionsErrorMessage = '';
                  });
                }

                // Create poll options
                final List<Map<String, dynamic>> options = [];
                for (final controller in optionControllers) {
                  options.add({
                    'text': controller.text.trim(),
                    'votes': 0,
                  });
                }

                // Update state in the parent widget
                setState(() {
                  _pollOptions = options;
                  // Store the expiry days for later use
                  _pollExpiryDays = selectedExpiryDays;

                  // Always update post content with the question
                  // If empty, keep the existing content
                  if (questionController.text.trim().isNotEmpty) {
                    _postController.text = questionController.text.trim();
                  }
                });

                // Close dialog
                Navigator.pop(dialogContext);

                // Show a snackbar to confirm poll creation
                _showSnackBar('Poll options added', backgroundColor: gradients[3][0]);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: gradients[3][0],
              ),
              child: Text(
                'Create Poll',
                style: GoogleFonts.poppins(
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityFeed() {
    // Use responsive padding based on screen size
    final horizontalPadding = ResponsiveLayout.getValueForScreenType<double>(
      context: context,
      mobile: 16,
      tablet: 24,
      desktop: 32,
    );

    final titleFontSize = ResponsiveLayout.getFontSizeForScreenType(
      context: context,
      mobile: 24,
      tablet: 28,
      desktop: 32,
    );

    // Use a simpler ListView approach to avoid potential issues with CustomScrollView
    return ListView(
      controller: _scrollController,
      physics: const BouncingScrollPhysics(),
      children: [
        Padding(
          padding: EdgeInsets.fromLTRB(horizontalPadding, 24, horizontalPadding, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Community Feed',
                style: GoogleFonts.poppins(
                  fontSize: titleFontSize * 0.85,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Connect with developers and share your projects',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.black54,
                ),
              ),
              const SizedBox(height: 12),
              // Category filters
              OptimizedAnimatedWidget(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  physics: const BouncingScrollPhysics(),
                  child: Row(
                    children: [
                      _buildCategoryChip('All', isSelected: true, gradient: gradients[0]),
                      _buildCategoryChip('Popular', gradient: gradients[1]),
                      _buildCategoryChip('Latest', gradient: gradients[2]),
                      _buildCategoryChip('Following', gradient: gradients[3]),
                      _buildCategoryChip('Trending', gradient: gradients[0]),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 12),
            ],
          ),
        ),

        // Directly render each post without using SliverList
        ..._posts.asMap().entries.map((entry) {
          final index = entry.key;
          final post = entry.value;
          return RepaintBoundary(
            key: ValueKey('post_${post['id']}'),
            child: OptimizedScrollItem(
              keepAlive: index < 3, // Keep only first 3 items alive for better performance
              child: _buildSimplePostCard(post),
            ),
          );
        }).toList(),

        // Loading indicator
        if (_isLoading)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(gradients[0][0]),
              ),
            ),
          ),
      ],
    );
  }

  // Enhanced post card with better UI and functionality
  Widget _buildSimplePostCard(Map<String, dynamic> post) {
    // Determine which gradient to use based on post
    final int postIndex = _posts.indexOf(post);
    final List<Color> postGradient = gradients[postIndex % gradients.length];

    // Use responsive padding based on screen size
    final horizontalPadding = ResponsiveLayout.getValueForScreenType<double>(
      context: context,
      mobile: 16,
      tablet: 24,
      desktop: 32,
    );

    return OptimizedAnimatedWidget(
      child: Container(
        margin: EdgeInsets.fromLTRB(horizontalPadding, 0, horizontalPadding, 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(10),
              blurRadius: 4,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Author info with more options button
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.pushNamed(context, '/public_profile');
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: postGradient,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: postGradient[0].withAlpha(40),
                          blurRadius: 4,
                          spreadRadius: 0,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    padding: const EdgeInsets.all(2),
                    child: CircleAvatar(
                      radius: 20,
                      backgroundColor: Colors.grey[300],
                      backgroundImage: (post['authorImage'] ?? post['authorAvatar'] ?? '').toString().isNotEmpty
                          ? NetworkImage(post['authorImage'] ?? post['authorAvatar'])
                          : null,
                      child: (post['authorImage'] ?? post['authorAvatar'] ?? '').toString().isEmpty
                          ? Icon(
                              Icons.person,
                              size: 24,
                              color: Colors.grey[600],
                            )
                          : null,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      GestureDetector(
                        onTap: () {
                          Navigator.pushNamed(context, '/public_profile');
                        },
                        child: Text(
                          post['author'] ?? post['authorName'] ?? 'Unknown User',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: const Color(0xFF6A11CB),
                          ),
                        ),
                      ),
                      Text(
                        post['time'] ?? 'Unknown time',
                        style: GoogleFonts.poppins(
                          color: Colors.black54,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.more_horiz, color: Colors.grey[700]),
                  onPressed: () {
                    _showPostOptions(context, post);
                  },
                ),
              ],
            ),
          ),

          // Post content based on type
          _buildPostContentByType(post, postGradient),

          // Divider
          Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Divider(color: Colors.grey[200], height: 1),
          ),

          // Post interactions - Wrap for better flexibility
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // Like button
                  InkWell(
                    onTap: () async {
                      final postId = post['id'];

                      // Prevent multiple taps
                      if (_likingPosts.contains(postId)) {
                        return;
                      }

                      _likingPosts.add(postId);

                      try {
                        // Get the UserContentService
                        final userContentService = Provider.of<UserContentService>(context, listen: false);

                        // Use optimized toggle method
                        final bool newLikeStatus = await userContentService.togglePostLike(postId);

                        if (mounted) {
                          setState(() {
                            final bool wasLiked = post['isLiked'] ?? false;
                            post['isLiked'] = newLikeStatus;

                            // Update like count based on the new status
                            final currentLikes = post['likes'] ?? 0;
                            if (newLikeStatus && !wasLiked) {
                              // Liked
                              post['likes'] = currentLikes + 1;
                            } else if (!newLikeStatus && wasLiked) {
                              // Unliked
                              post['likes'] = (currentLikes > 0) ? currentLikes - 1 : 0;
                            }
                          });
                        }
                      } catch (e) {
                        debugPrint('Error toggling like: $e');
                        if (mounted) {
                          _showSnackBar('Error: $e', backgroundColor: Colors.red);
                        }
                      } finally {
                        // Remove from debounce set after a delay
                        Future.delayed(const Duration(milliseconds: 1000), () {
                          _likingPosts.remove(postId);
                        });
                      }
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            post['isLiked'] ? Icons.favorite : Icons.favorite_border,
                            color: post['isLiked'] ? Colors.red : Colors.grey[700],
                            size: 18,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${post['likes']}',
                            style: GoogleFonts.poppins(
                              color: post['isLiked'] ? Colors.red : Colors.grey[700],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Comment button
                  InkWell(
                    onTap: () {
                      _showCommentsSheet(context, post);
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.chat_bubble_outline,
                            color: Colors.grey[700],
                            size: 18,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${post['comments']}',
                            style: GoogleFonts.poppins(
                              color: Colors.grey[700],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Share button
                  InkWell(
                    onTap: () {
                      _sharePost(post);
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.share_outlined,
                            color: Colors.grey[700],
                            size: 18,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Share',
                            style: GoogleFonts.poppins(
                              color: Colors.grey[700],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Save button
                  FutureBuilder<bool>(
                    future: Provider.of<UserContentService>(context, listen: false).isPostSaved(post['id']),
                    builder: (context, snapshot) {
                      final bool isSaved = snapshot.data ?? false;

                      return InkWell(
                        onTap: () {
                          _toggleSavePost(post['id'], isSaved);
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                isSaved ? Icons.bookmark : Icons.bookmark_border,
                                color: isSaved ? postGradient[0] : Colors.grey[700],
                                size: 18,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                isSaved ? 'Saved' : 'Save',
                                style: GoogleFonts.poppins(
                                  color: isSaved ? postGradient[0] : Colors.grey[700],
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          // Comments preview - simplified to avoid repeated API calls
          if (post['comments'] > 0)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: GestureDetector(
                onTap: () {
                  _showCommentsSheet(context, post);
                },
                child: Row(
                  children: [
                    Icon(
                      Icons.comment_outlined,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'View ${post['comments']} comment${post['comments'] > 1 ? 's' : ''}',
                      style: GoogleFonts.poppins(
                        fontSize: 13,
                        color: Colors.black54,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    ));
  }

  Widget _buildCategoryChip(String label, {bool isSelected = false, required List<Color> gradient}) {
    return Container(
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        gradient: isSelected
            ? LinearGradient(
                colors: gradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: isSelected ? null : Colors.grey[200],
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: gradient[0].withAlpha(50),
                  blurRadius: 8,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {},
          borderRadius: BorderRadius.circular(25),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
            child: Text(
              label,
              style: GoogleFonts.poppins(
                color: isSelected ? Colors.white : Colors.black87,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                fontSize: 13,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Determine which content builder to use based on post type
  Widget _buildPostContentByType(Map<String, dynamic> post, List<Color> gradient) {
    final String postType = post['type'] ?? 'regular';

    switch (postType) {
      case 'code':
        return _buildCodePostContent(post, gradient);
      case 'poll':
        return _buildPollPostContent(post, gradient);
      case 'question':
        return _buildQuestionPostContent(post, gradient);
      case 'event':
        return _buildEventPostContent(post, gradient);
      case 'regular':
      default:
        return _buildRegularPostContent(post, gradient);
    }
  }


  Widget _buildRegularPostContent(Map<String, dynamic> post, List<Color> gradient) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Post text
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            post['content'],
            style: GoogleFonts.poppins(
              fontSize: 14,
              height: 1.5,
              color: Colors.black87,
            ),
          ),
        ),

        // Post image if available
        if (post['hasImage'])
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 4,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Container(
                width: double.infinity,
                height: 250,
                color: Colors.grey[200],
                child: Center(
                  child: Icon(
                    Icons.image,
                    size: 50,
                    color: Colors.grey[400],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildCodePostContent(Map<String, dynamic> post, List<Color> gradient) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Post text
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Text(
            post['content'],
            style: GoogleFonts.poppins(
              fontSize: 14,
              height: 1.5,
              color: Colors.black87,
            ),
          ),
        ),

        // Code block with enhanced UI
        Container(
          margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          decoration: BoxDecoration(
            color: const Color(0xFF1E1E1E), // VS Code dark theme color
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: gradient[0].withAlpha(50),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(20),
                blurRadius: 8,
                spreadRadius: 0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Code header with language and copy button
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: gradient,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(11),
                    topRight: Radius.circular(11),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(51), // 0.2 opacity
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: const Icon(
                        Icons.code,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Text(
                      post['codeLanguage'] ?? 'Code Snippet',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const Spacer(),
                    // Copy button with improved UI
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          // Copy code to clipboard
                          Clipboard.setData(ClipboardData(text: post['code'] ?? ''));
                          // Show snackbar
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Code copied to clipboard',
                                style: GoogleFonts.poppins(),
                              ),
                              backgroundColor: gradient[0],
                              behavior: SnackBarBehavior.floating,
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        borderRadius: BorderRadius.circular(6),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(38), // 0.15 opacity
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.content_copy,
                                color: Colors.white,
                                size: 14,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'Copy',
                                style: GoogleFonts.poppins(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Line numbers and code content
              Container(
                constraints: const BoxConstraints(
                  maxHeight: 300, // Limit height and add scrolling for long code
                ),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: IntrinsicWidth(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Line numbers
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: _generateLineNumbers(post['code'] ?? ''),
                            ),
                            const SizedBox(width: 12),
                            // Code content
                            ConstrainedBox(
                              constraints: BoxConstraints(
                                minWidth: MediaQuery.of(context).size.width * 0.6,
                              ),
                              child: Text(
                                post['code'] ?? 'print("Hello World")',
                                style: GoogleFonts.firaCode(
                                  color: Colors.white,
                                  fontSize: 13,
                                  height: 1.5,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to generate line numbers for code
  List<Widget> _generateLineNumbers(String code) {
    final List<String> lines = code.split('\n');
    return List.generate(
      lines.length,
      (index) => Container(
        width: 25,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 8, bottom: 4.5),
        child: Text(
          '${index + 1}',
          style: GoogleFonts.firaCode(
            color: Colors.grey[500],
            fontSize: 13,
            height: 1.5,
          ),
        ),
      ),
    );
  }

  Widget _buildPollPostContent(Map<String, dynamic> post, List<Color> gradient) {
    // Cast the dynamic list to List<Map<String, dynamic>> properly
    final List<dynamic> rawOptions = post['pollOptions'] ?? [];
    final List<Map<String, dynamic>> options = rawOptions
        .map((option) => option is Map ? Map<String, dynamic>.from(option) : <String, dynamic>{})
        .toList();

    // Calculate total votes safely
    final int totalVotes = options.fold(0, (total, option) =>
        total + (option['votes'] is int ? option['votes'] as int : 0));

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Poll question with icon
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: gradient[0].withAlpha(20),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.poll_outlined,
                  color: gradient[0],
                  size: 18,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Poll',
                      style: GoogleFonts.poppins(
                        color: gradient[0],
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      post['content'],
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        height: 1.4,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Poll options with enhanced UI
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey[200]!,
                width: 1,
              ),
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                ...options.asMap().entries.map((entry) {
                  final int index = entry.key;
                  final Map<String, dynamic> option = entry.value;
                  final double percentage = totalVotes > 0 ? (option['votes'] as int) / totalVotes * 100 : 0;
                  final bool isTopVoted = totalVotes > 0 &&
                      (option['votes'] as int) == options.map((o) => o['votes'] as int).reduce((a, b) => a > b ? a : b);

                  return Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: InkWell(
                      onTap: () => _handlePollVote(post, index),
                      borderRadius: BorderRadius.circular(10),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: isTopVoted ? gradient[0].withAlpha(10) : Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: isTopVoted ? gradient[0].withAlpha(50) : Colors.grey[200]!,
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: isTopVoted ? gradient[0].withAlpha(10) : Colors.black.withAlpha(5),
                              blurRadius: 4,
                              spreadRadius: 0,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                // Option letter in circle
                                Container(
                                  width: 28,
                                  height: 28,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: isTopVoted ? gradient[0] : Colors.grey[200],
                                  ),
                                  child: Center(
                                    child: Text(
                                      String.fromCharCode(65 + index), // A, B, C, etc.
                                      style: GoogleFonts.poppins(
                                        color: isTopVoted ? Colors.white : Colors.black87,
                                        fontWeight: FontWeight.w600,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    option['text'],
                                    style: GoogleFonts.poppins(
                                      color: Colors.black87,
                                      fontSize: 14,
                                      fontWeight: isTopVoted ? FontWeight.w600 : FontWeight.w500,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: isTopVoted ? gradient[0] : Colors.grey[200],
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '${percentage.toStringAsFixed(1)}%',
                                    style: GoogleFonts.poppins(
                                      color: isTopVoted ? Colors.white : Colors.black87,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),
                            // Progress bar
                            Stack(
                              children: [
                                Container(
                                  height: 8,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                                Container(
                                  height: 8,
                                  width: MediaQuery.of(context).size.width * (percentage / 100) * 0.7,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: isTopVoted
                                          ? gradient
                                          : [gradient[0].withAlpha(150), gradient[1].withAlpha(150)],
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                    ),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),

                // Poll info
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Row(
                    children: [
                      Icon(
                        Icons.how_to_vote_outlined,
                        color: Colors.grey[600],
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        '$totalVotes votes',
                        style: GoogleFonts.poppins(
                          color: Colors.grey[600],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Icon(
                        Icons.access_time,
                        color: Colors.grey[600],
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        post['pollTimeLeft'] ?? '2 days left',
                        style: GoogleFonts.poppins(
                          color: Colors.grey[600],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionPostContent(Map<String, dynamic> post, List<Color> gradient) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [gradient[0].withAlpha(20), gradient[1].withAlpha(5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: gradient[0].withAlpha(30),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.help_outline,
                color: gradient[0],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Question',
                style: GoogleFonts.poppins(
                  color: gradient[0],
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: gradient[0].withAlpha(20),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  post['questionCategory'] ?? 'Android',
                  style: GoogleFonts.poppins(
                    color: gradient[0],
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            post['questionTitle'] ?? 'How do I implement Firebase Authentication?',
            style: GoogleFonts.poppins(
              color: Colors.black87,
              fontSize: 16,
              fontWeight: FontWeight.w600,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            post['content'],
            style: GoogleFonts.poppins(
              color: Colors.black87,
              fontSize: 14,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                Icons.comment_outlined,
                color: Colors.grey[600],
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                '${post['answers'] ?? 5} answers',
                style: GoogleFonts.poppins(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
              const SizedBox(width: 16),
              Icon(
                Icons.remove_red_eye_outlined,
                color: Colors.grey[600],
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                '${post['views'] ?? 42} views',
                style: GoogleFonts.poppins(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEventPostContent(Map<String, dynamic> post, List<Color> gradient) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Event description
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              post['content'],
              style: GoogleFonts.poppins(
                fontSize: 14,
                height: 1.5,
                color: Colors.black87,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Event card
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: gradient[0].withAlpha(50),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Event image
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(11),
                    topRight: Radius.circular(11),
                  ),
                  child: Container(
                    height: 150,
                    width: double.infinity,
                    color: Colors.grey[200],
                    child: Center(
                      child: Icon(
                        Icons.event,
                        size: 40,
                        color: Colors.grey[400],
                      ),
                    ),
                  ),
                ),

                // Event details
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(11),
                      bottomRight: Radius.circular(11),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Event title
                      Text(
                        post['eventTitle'] ?? 'Flutter Developer Meetup',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Event date & time
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: gradient[0].withAlpha(20),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Icon(
                              Icons.calendar_today,
                              color: gradient[0],
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                post['eventDate'] ?? 'Saturday, June 15, 2024',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                              ),
                              Text(
                                post['eventTime'] ?? '2:00 PM - 5:00 PM',
                                style: GoogleFonts.poppins(
                                  fontSize: 13,
                                  color: Colors.black54,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Event location
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: gradient[0].withAlpha(20),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Icon(
                              Icons.location_on,
                              color: gradient[0],
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                post['eventLocation'] ?? 'Tech Hub Coworking Space',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                              ),
                              Text(
                                post['eventAddress'] ?? '123 Innovation Street, San Francisco',
                                style: GoogleFonts.poppins(
                                  fontSize: 13,
                                  color: Colors.black54,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      // Action buttons
                      Row(
                        children: [
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: gradient,
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(10),
                                boxShadow: [
                                  BoxShadow(
                                    color: gradient[0].withAlpha(50),
                                    blurRadius: 8,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: ElevatedButton(
                                onPressed: () {},
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.transparent,
                                  foregroundColor: Colors.white,
                                  elevation: 0,
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                                child: Text(
                                  'RSVP',
                                  style: GoogleFonts.poppins(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: Colors.grey[300]!,
                                width: 1,
                              ),
                            ),
                            child: IconButton(
                              onPressed: () {},
                              icon: Icon(
                                Icons.share_outlined,
                                color: Colors.grey[700],
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: Colors.grey[300]!,
                                width: 1,
                              ),
                            ),
                            child: IconButton(
                              onPressed: () {},
                              icon: Icon(
                                Icons.calendar_month_outlined,
                                color: Colors.grey[700],
                              ),
                            ),
                          ),
                        ],
                      ),

                      // Attendees
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          SizedBox(
                            width: 130, // Fixed width for the Stack
                            height: 40, // Fixed height for the Stack
                            child: Stack(
                              children: [
                                for (int i = 0; i < 4; i++)
                                  Positioned(
                                    left: i * 20.0,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: Colors.white,
                                          width: 2,
                                        ),
                                        shape: BoxShape.circle,
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(15),
                                        child: Container(
                                          width: 30,
                                          height: 30,
                                          color: Colors.grey[300],
                                          child: Icon(
                                            Icons.person,
                                            size: 20,
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                Positioned(
                                  left: 4 * 20.0,
                                  child: Container(
                                    width: 30,
                                    height: 30,
                                    decoration: BoxDecoration(
                                      color: gradient[0],
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: Colors.white,
                                        width: 2,
                                      ),
                                    ),
                                    child: Center(
                                      child: Text(
                                        '+${post['attendeeCount'] ?? 18}',
                                        style: GoogleFonts.poppins(
                                          color: Colors.white,
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 16),
                          Text(
                            '${post['attendeeCount'] ?? 18} people attending',
                            style: GoogleFonts.poppins(
                              color: Colors.black54,
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  void _showPostOptions(BuildContext context, Map<String, dynamic> post) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(20),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            _buildOptionItem(
              Icons.bookmark_add_outlined,
              post['isSaved'] == true ? 'Unsave post' : 'Save post',
              gradients[0],
              onTap: () {
                Navigator.pop(context);
                _handleSavePost(post['id']);
              },
            ),
            _buildOptionItem(
              Icons.share_outlined,
              'Share post',
              gradients[1],
              onTap: () {
                Navigator.pop(context);
                _handleSharePost(post);
              },
            ),
            _buildOptionItem(
              Icons.report_outlined,
              'Report post',
              gradients[2],
              onTap: () {
                Navigator.pop(context);
                _handleReportPost(post);
              },
            ),
            // Disabled for now - will be enabled in future
            // _buildOptionItem(
            //   Icons.block_outlined,
            //   'Hide posts from this user',
            //   gradients[3],
            //   onTap: () {
            //     Navigator.pop(context);
            //     _handleHidePostsFromUser(post);
            //   },
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionItem(IconData icon, String label, List<Color> gradient, {VoidCallback? onTap}) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradient,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: gradient[0].withAlpha(40),
              blurRadius: 8,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(icon, color: Colors.white, size: 20),
      ),
      title: Text(
        label,
        style: GoogleFonts.poppins(
          color: Colors.black87,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap ?? () {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '$label successful',
              style: GoogleFonts.poppins(),
            ),
            behavior: SnackBarBehavior.floating,
            backgroundColor: gradient[0],
          ),
        );
      },
    );
  }

  // Handler methods for post options
  Future<void> _handleSavePost(String postId) async {
    try {
      final userContentService = Provider.of<UserContentService>(context, listen: false);
      final postIndex = _posts.indexWhere((post) => post['id'] == postId);

      if (postIndex == -1) return;

      final bool isCurrentlySaved = _posts[postIndex]['isSaved'] ?? false;

      if (isCurrentlySaved) {
        // Unsave post
        final result = await userContentService.unsavePost(postId);
        if (result) {
          setState(() {
            _posts[postIndex]['isSaved'] = false;
          });
          _showSnackBar('Post removed from saved items', backgroundColor: Colors.grey[700]!);
        } else {
          _showSnackBar('Failed to remove from saved items', backgroundColor: Colors.red);
        }
      } else {
        // Save post
        final result = await userContentService.savePost(postId);
        if (result) {
          setState(() {
            _posts[postIndex]['isSaved'] = true;
          });
          _showSnackBar('Post saved successfully', backgroundColor: Colors.green);
        } else {
          _showSnackBar('Failed to save post', backgroundColor: Colors.red);
        }
      }
    } catch (e) {
      debugPrint('Error handling save post: $e');
      _showSnackBar('Error: $e', backgroundColor: Colors.red);
    }
  }

  Future<void> _handleSharePost(Map<String, dynamic> post) async {
    try {
      final String postId = post['id'];
      final String postContent = post['content'] ?? '';
      final String authorName = post['author'] ?? post['authorName'] ?? 'Unknown';

      final String description = postContent.length > 100
          ? '${postContent.substring(0, 97)}...'
          : postContent;

      final String shareText = 'Check out this post by $authorName on Asatu!\n\n$description';
      final String shareUrl = 'https://asatu.com/post/$postId';

      // Use share_plus to share the content
      await Share.share(
        '$shareText\n\n$shareUrl',
        subject: 'Asatu Post by $authorName',
      );
    } catch (e) {
      debugPrint('Error sharing post: $e');
      _showSnackBar('Error sharing post: $e', backgroundColor: Colors.red);
    }
  }

  Future<void> _handleReportPost(Map<String, dynamic> post) async {
    String? selectedReason;
    final TextEditingController customReasonController = TextEditingController();

    // Show report dialog
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            'Report Post',
            style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Why are you reporting this post?',
                  style: GoogleFonts.poppins(),
                ),
                const SizedBox(height: 16),
                ...['Spam', 'Inappropriate content', 'Harassment', 'False information', 'Other'].map(
                  (reason) => RadioListTile<String>(
                    title: Text(reason, style: GoogleFonts.poppins()),
                    value: reason,
                    groupValue: selectedReason,
                    onChanged: (value) {
                      setState(() {
                        selectedReason = value;
                      });
                    },
                  ),
                ),
                // Show custom description field when "Other" is selected
                if (selectedReason == 'Other') ...[
                  const SizedBox(height: 16),
                  Text(
                    'Please describe the issue:',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: customReasonController,
                    decoration: InputDecoration(
                      hintText: 'Describe why you\'re reporting this post...',
                      hintStyle: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey[500],
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.all(12),
                    ),
                    maxLines: 3,
                    maxLength: 200,
                    style: GoogleFonts.poppins(fontSize: 14),
                  ),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                customReasonController.dispose();
                Navigator.pop(context);
              },
              child: Text('Cancel', style: GoogleFonts.poppins()),
            ),
            ElevatedButton(
              onPressed: selectedReason != null
                  ? () async {
                      // Validate custom reason if "Other" is selected
                      if (selectedReason == 'Other' && customReasonController.text.trim().isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Please describe the issue',
                              style: GoogleFonts.poppins(),
                            ),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }

                      final String finalReason = selectedReason == 'Other'
                          ? 'Other: ${customReasonController.text.trim()}'
                          : selectedReason!;

                      customReasonController.dispose();
                      Navigator.pop(context);
                      await _submitReport(post, finalReason);
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
              ),
              child: Text('Report', style: GoogleFonts.poppins()),
            ),
          ],
        ),
      ),
    );
  }

  // Handle poll voting
  Future<void> _handlePollVote(Map<String, dynamic> post, int optionIndex) async {
    try {
      final String postId = post['id'] ?? '';
      final String currentUserId = FirebaseAuth.instance.currentUser?.uid ?? '';

      if (currentUserId.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Please login to vote',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Check if poll has ended
      final String pollTimeLeft = post['pollTimeLeft'] ?? '';
      if (pollTimeLeft == 'Poll ended') {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'This poll has ended',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Update UI immediately for better UX
      final String optionText = post['pollOptions'][optionIndex]['text'] ?? 'Option ${optionIndex + 1}';
      setState(() {
        final List<dynamic> pollOptions = List.from(post['pollOptions'] ?? []);
        if (optionIndex < pollOptions.length) {
          final Map<String, dynamic> option = Map<String, dynamic>.from(pollOptions[optionIndex]);
          option['votes'] = (option['votes'] ?? 0) + 1;
          pollOptions[optionIndex] = option;
          post['pollOptions'] = pollOptions;
        }
      });

      // Show success message immediately
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Voted for "$optionText"',
            style: GoogleFonts.poppins(),
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );

      // Process vote in background
      _processVoteInBackground(postId, currentUserId, optionIndex, post);
    } catch (e) {
      debugPrint('Error handling poll vote: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Error voting: $e',
            style: GoogleFonts.poppins(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Process vote in background for better performance
  Future<void> _processVoteInBackground(String postId, String currentUserId, int optionIndex, Map<String, dynamic> post) async {
    try {
      // Check if user has already voted
      final DocumentSnapshot voteDoc = await FirebaseFirestore.instance
          .collection('poll_votes')
          .doc('${postId}_$currentUserId')
          .get();

      if (voteDoc.exists) {
        // Revert UI change if user already voted
        if (mounted) {
          setState(() {
            final List<dynamic> pollOptions = List.from(post['pollOptions'] ?? []);
            if (optionIndex < pollOptions.length) {
              final Map<String, dynamic> option = Map<String, dynamic>.from(pollOptions[optionIndex]);
              option['votes'] = ((option['votes'] ?? 1) - 1).clamp(0, double.infinity).toInt();
              pollOptions[optionIndex] = option;
              post['pollOptions'] = pollOptions;
            }
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'You have already voted in this poll',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // Use batch write for better performance
      final WriteBatch batch = FirebaseFirestore.instance.batch();

      // Record the vote
      final DocumentReference voteRef = FirebaseFirestore.instance
          .collection('poll_votes')
          .doc('${postId}_$currentUserId');

      batch.set(voteRef, {
        'postId': postId,
        'userId': currentUserId,
        'optionIndex': optionIndex,
        'timestamp': FieldValue.serverTimestamp(),
      });

      // Update the poll option vote count using increment
      final DocumentReference postRef = FirebaseFirestore.instance
          .collection('posts')
          .doc(postId);

      // Use field path to increment specific option vote count
      batch.update(postRef, {
        'pollOptions.$optionIndex.votes': FieldValue.increment(1),
      });

      // Commit batch
      await batch.commit();

      debugPrint('Vote processed successfully for post $postId, option $optionIndex');
    } catch (e) {
      debugPrint('Error processing vote in background: $e');

      // Revert UI change on error
      if (mounted) {
        setState(() {
          final List<dynamic> pollOptions = List.from(post['pollOptions'] ?? []);
          if (optionIndex < pollOptions.length) {
            final Map<String, dynamic> option = Map<String, dynamic>.from(pollOptions[optionIndex]);
            option['votes'] = ((option['votes'] ?? 1) - 1).clamp(0, double.infinity).toInt();
            pollOptions[optionIndex] = option;
            post['pollOptions'] = pollOptions;
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to record vote. Please try again.',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _submitReport(Map<String, dynamic> post, String reason) async {
    try {
      final userContentService = Provider.of<UserContentService>(context, listen: false);
      final result = await userContentService.reportPost(post['id'], reason);

      if (result['success']) {
        _showSnackBar('Post reported successfully', backgroundColor: Colors.orange);
      } else {
        _showSnackBar('Failed to report post', backgroundColor: Colors.red);
      }
    } catch (e) {
      debugPrint('Error reporting post: $e');
      _showSnackBar('Error reporting post: $e', backgroundColor: Colors.red);
    }
  }

  Future<void> _handleHidePostsFromUser(Map<String, dynamic> post) async {
    try {
      final String authorId = post['authorId'] ?? '';
      final String authorName = post['author'] ?? post['authorName'] ?? 'this user';

      // Show confirmation dialog
      final bool? confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            'Hide Posts',
            style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
          ),
          content: Text(
            'Hide all posts from $authorName? You can undo this in your settings.',
            style: GoogleFonts.poppins(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text('Cancel', style: GoogleFonts.poppins()),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: Text('Hide', style: GoogleFonts.poppins(color: Colors.red)),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        final userContentService = Provider.of<UserContentService>(context, listen: false);
        final result = await userContentService.hidePostsFromUser(authorId);

        if (result) {
          // Remove all posts from this user from the local list
          setState(() {
            _posts.removeWhere((p) => p['authorId'] == authorId);
          });
          _showSnackBar('Posts from $authorName hidden', backgroundColor: Colors.grey[700]!);
        } else {
          _showSnackBar('Failed to hide posts', backgroundColor: Colors.red);
        }
      }
    } catch (e) {
      debugPrint('Error hiding posts from user: $e');
      _showSnackBar('Error: $e', backgroundColor: Colors.red);
    }
  }

  void _showCommentsSheet(BuildContext context, Map<String, dynamic> post) {
    // Determine which gradient to use based on post
    final int postIndex = _posts.indexOf(post);
    final List<Color> commentGradient = gradients[postIndex % gradients.length];

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      builder: (context) => CommentsBottomSheetWidget(
        post: post,
        commentGradient: commentGradient,
        onCommentAdded: (int newCommentCount) {
          // Update the post comment count in the main list
          if (mounted) {
            setState(() {
              post['comments'] = newCommentCount;
              post['commentsCount'] = newCommentCount; // Also update commentsCount field
            });
          }
        },
      ),
    );
  }

  // Refresh a single post's data from Firebase
  Future<void> _refreshSinglePost(String postId) async {
    try {
      final postDoc = await FirebaseFirestore.instance
          .collection('posts')
          .doc(postId)
          .get();

      if (postDoc.exists && mounted) {
        final updatedData = postDoc.data() as Map<String, dynamic>;

        // Find the post in the current list and update it
        final postIndex = _posts.indexWhere((post) => post['id'] == postId);
        if (postIndex != -1) {
          setState(() {
            // Update comment count and other fields
            _posts[postIndex]['comments'] = updatedData['commentsCount'] ?? 0;
            _posts[postIndex]['commentsCount'] = updatedData['commentsCount'] ?? 0;
            _posts[postIndex]['likes'] = updatedData['likesCount'] ?? 0;
            _posts[postIndex]['likesCount'] = updatedData['likesCount'] ?? 0;
          });
        }
      }
    } catch (e) {
      debugPrint('Error refreshing single post: $e');
    }
  }



  // Format timestamp to relative time
  String _formatTimeAgo(dynamic timestamp) {
    if (timestamp == null) return 'Just now';

    final DateTime time = timestamp is DateTime
        ? timestamp
        : (timestamp as Timestamp).toDate();

    final Duration difference = DateTime.now().difference(time);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  // Toggle save/unsave post
  Future<void> _toggleSavePost(String postId, bool isCurrentlySaved) async {
    try {
      debugPrint('Toggling save post: $postId, currently saved: $isCurrentlySaved');
      final userContentService = Provider.of<UserContentService>(context, listen: false);

      if (isCurrentlySaved) {
        // Unsave post
        debugPrint('Unsaving post: $postId');
        final result = await userContentService.unsavePost(postId);
        debugPrint('Unsave result: $result');
        if (result) {
          _showSnackBar('Post removed from saved items', backgroundColor: Colors.grey[700]!);
          // Update the post's saved status in the local list
          final postIndex = _posts.indexWhere((post) => post['id'] == postId);
          if (postIndex != -1) {
            setState(() {
              _posts[postIndex]['isSaved'] = false;
            });
          }
        } else {
          _showSnackBar('Failed to remove from saved items', backgroundColor: Colors.red);
        }
      } else {
        // Save post
        debugPrint('Saving post: $postId');
        final result = await userContentService.savePost(postId);
        debugPrint('Save result: $result');
        if (result) {
          _showSnackBar('Post saved successfully', backgroundColor: Colors.green);
          // Update the post's saved status in the local list
          final postIndex = _posts.indexWhere((post) => post['id'] == postId);
          if (postIndex != -1) {
            setState(() {
              _posts[postIndex]['isSaved'] = true;
            });
          }
        } else {
          _showSnackBar('Failed to save post', backgroundColor: Colors.red);
        }
      }

    } catch (e) {
      debugPrint('Error toggling save post: $e');
      _showSnackBar('Error: $e', backgroundColor: Colors.red);
    }
  }

  // Share post
  void _sharePost(Map<String, dynamic> post) async {
    try {
      // Create a dynamic link for the post
      final String postId = post['id'];
      final String postContent = post['content'] ?? '';
      final String authorName = post['author'] ?? post['authorName'] ?? 'Unknown';

      // Create a short description for the share
      final String description = postContent.length > 100
          ? '${postContent.substring(0, 97)}...'
          : postContent;

      // Create the share text
      final String shareText = 'Check out this post by $authorName on Asatu!\n\n$description';

      // Create the share URL (this would be your app's deep link)
      final String shareUrl = 'https://asatu.com/post/$postId';

      // Use share_plus to share the content
      await Share.share(
        '$shareText\n\n$shareUrl',
        subject: 'Asatu Post by $authorName',
      );
    } catch (e) {
      debugPrint('Error sharing post: $e');
      if (mounted) {
        _showSnackBar('Error sharing post: $e', backgroundColor: Colors.red);
      }
    }
  }







  Widget _buildForumsAndGroups() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 3,
          child: ListView(
            padding: const EdgeInsets.all(16),
            physics: const BouncingScrollPhysics(),
            children: [
              Text(
                'Popular Forums',
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 20),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                physics: const BouncingScrollPhysics(),
                child: Row(
                  children: [
                    _buildCategoryChip('All', isSelected: true, gradient: gradients[2]),
                    _buildCategoryChip('Android Development', gradient: gradients[0]),
                    _buildCategoryChip('Sketchware', gradient: gradients[1]),
                    _buildCategoryChip('UI/UX Design', gradient: gradients[3]),
                    _buildCategoryChip('Firebase', gradient: gradients[0]),
                    _buildCategoryChip('Monetization', gradient: gradients[1]),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              _buildForumThreadCard(
                title: 'Material Design 3 Implementation',
                description: 'Best practices for Material Design 3 implementation in Android apps',
                author: 'Jessica Wong',
                authorImage: 'https://randomuser.me/api/portraits/women/32.jpg',
                replies: 42,
                views: '3.2k',
                time: '2d ago',
                tags: ['Android', 'Hot'],
                gradient: gradients[0],
              ),
              const SizedBox(height: 16),
              _buildForumThreadCard(
                title: 'Flutter State Management',
                description: 'What\'s your preferred state management solution for Flutter apps?',
                author: 'David Chen',
                authorImage: 'https://randomuser.me/api/portraits/men/32.jpg',
                replies: 78,
                views: '5.4k',
                time: '1d ago',
                tags: ['Flutter', 'Discussion'],
                gradient: gradients[1],
              ),
              const SizedBox(height: 16),
              _buildForumThreadCard(
                title: 'Firebase Authentication Issues',
                description: 'Having trouble with Firebase Auth in my Android app. Anyone else experiencing this?',
                author: 'Priya Sharma',
                authorImage: 'https://randomuser.me/api/portraits/women/63.jpg',
                replies: 23,
                views: '1.8k',
                time: '4h ago',
                tags: ['Firebase', 'Help'],
                gradient: gradients[2],
              ),
            ],
          ),
        ),
        Expanded(
          flex: 2,
          child: Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.grey.shade300,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 4,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ShaderMask(
                      shaderCallback: (bounds) => LinearGradient(
                        colors: gradients[3],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ).createShader(bounds),
                      child: Text(
                        'Start a Discussion',
                        style: GoogleFonts.poppins(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Share your thoughts, ask questions, or start a discussion about app development topics.',
                      style: GoogleFonts.poppins(
                        color: Colors.black54,
                        fontSize: 14,
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: gradients[3],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: gradients[3][0].withAlpha(50),
                            blurRadius: 8,
                            spreadRadius: 0,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ElevatedButton.icon(
                        onPressed: () {},
                        icon: const Icon(Icons.add, size: 18),
                        label: Text(
                          'Create New Topic',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
                          elevation: 0,
                          minimumSize: const Size(double.infinity, 50),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 30),
                    Text(
                      'Your Groups',
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildGroupItem(
                      icon: Icons.phone_android,
                      name: 'Android UI Designers',
                      members: '1.2k',
                      newPosts: '24',
                      gradient: gradients[0],
                    ),
                    _buildGroupItem(
                      icon: Icons.local_fire_department,
                      name: 'Firebase Enthusiasts',
                      members: '843',
                      newPosts: '12',
                      gradient: gradients[1],
                    ),
                    _buildGroupItem(
                      icon: Icons.code,
                      name: 'Sketchware Pros',
                      members: '567',
                      newPosts: '8',
                      gradient: gradients[2],
                    ),
                    const SizedBox(height: 20),
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: gradients[0],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: gradients[0][0].withAlpha(50),
                            blurRadius: 8,
                            spreadRadius: 0,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ElevatedButton.icon(
                        onPressed: () {},
                        icon: const Icon(Icons.search, size: 18),
                        label: Text(
                          'Find More Groups',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
                          elevation: 0,
                          minimumSize: const Size(double.infinity, 50),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }



  Widget _buildForumThreadCard({
    required String title,
    required String description,
    required String author,
    required String authorImage,
    required int replies,
    required String views,
    required String time,
    required List<String> tags,
    required List<Color> gradient,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Stats column
            SizedBox(
              width: 80,
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: gradient,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: gradient[0].withAlpha(40),
                          blurRadius: 8,
                          spreadRadius: 0,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Text(
                          replies.toString(),
                          style: GoogleFonts.poppins(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'replies',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '$views views',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            // Content column
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tags
                  Wrap(
                    spacing: 8,
                    children: tags.map((tag) => _buildTag(tag, gradient[0])).toList(),
                  ),
                  const SizedBox(height: 12),
                  // Title
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Description
                  Text(
                    description,
                    style: GoogleFonts.poppins(
                      color: Colors.black54,
                      fontSize: 14,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Author info
                  Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: gradient,
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          shape: BoxShape.circle,
                        ),
                        padding: const EdgeInsets.all(2),
                        child: CircleAvatar(
                          radius: 12,
                          backgroundColor: Colors.grey[300],
                          child: Icon(
                            Icons.person,
                            size: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              author,
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                                fontSize: 13,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              'Started $time',
                              style: GoogleFonts.poppins(
                                color: Colors.black54,
                                fontSize: 13,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTag(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withAlpha(30),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        label,
        style: GoogleFonts.poppins(
          color: color,
          fontSize: 11,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildGroupItem({
    required IconData icon,
    required String name,
    required String members,
    required String newPosts,
    required List<Color> gradient,
  }) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradient,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: gradient[0].withAlpha(40),
              blurRadius: 8,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(icon, color: Colors.white),
      ),
      title: Text(
        name,
        style: GoogleFonts.poppins(
          fontWeight: FontWeight.w600,
          color: Colors.black87,
          fontSize: 14,
        ),
      ),
      subtitle: Text(
        '$members members • $newPosts new posts',
        style: GoogleFonts.poppins(
          color: Colors.black54,
          fontSize: 12,
        ),
      ),
      trailing: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.arrow_forward_ios,
          color: Colors.grey[700],
          size: 14,
        ),
      ),
      onTap: () {},
    );
  }





  Widget _buildMentorship() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: ListView(
            padding: const EdgeInsets.all(16),
            physics: const BouncingScrollPhysics(),
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.grey.shade300,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(10),
                      blurRadius: 4,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Find Your Mentor',
                      style: GoogleFonts.poppins(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Connect with experienced developers who can guide you on your journey.',
                      style: GoogleFonts.poppins(
                        color: Colors.black54,
                        fontSize: 14,
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 24),
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      physics: const BouncingScrollPhysics(),
                      child: Row(
                        children: [
                          _buildCategoryChip('All Expertise', isSelected: true, gradient: gradients[0]),
                          _buildCategoryChip('Android Dev', gradient: gradients[1]),
                          _buildCategoryChip('Sketchware', gradient: gradients[2]),
                          _buildCategoryChip('Firebase', gradient: gradients[3]),
                          _buildCategoryChip('UI/UX', gradient: gradients[0]),
                          _buildCategoryChip('Monetization', gradient: gradients[1]),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      decoration: InputDecoration(
                        hintText: 'Search mentors by name or expertise...',
                        prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide(color: gradients[0][0]),
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                        hintStyle: GoogleFonts.poppins(color: Colors.grey.shade500),
                      ),
                      style: GoogleFonts.poppins(color: Colors.black87),
                    ),
                    const SizedBox(height: 24),
                    Row(
                      children: [
                        Expanded(
                          child: _buildCompactMentorCard(
                            name: 'Marcus Chen',
                            expertise: 'Firebase & Backend Expert',
                            rating: 4.8,
                            reviews: 124,
                            imageUrl: 'https://randomuser.me/api/portraits/men/85.jpg',
                            skills: ['Firebase', 'Authentication'],
                            isTopMentor: true,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildCompactMentorCard(
                            name: 'Priya Sharma',
                            expertise: 'UI/UX & Frontend Expert',
                            rating: 5.0,
                            reviews: 97,
                            imageUrl: 'https://randomuser.me/api/portraits/women/63.jpg',
                            skills: ['UI Design', 'Material Design'],
                            isTopMentor: true,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              _buildMentorshipSidebar(),
            ],
          ),
        ),
      ],
    );
  }

  // Add the missing _buildMentorshipSidebar method
  Widget _buildMentorshipSidebar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Become a Mentor',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Share your knowledge and help others grow in their development journey.',
            style: GoogleFonts.poppins(
              color: Colors.black54,
              fontSize: 14,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 20),
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: gradients[2],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: gradients[2][0].withAlpha(50),
                  blurRadius: 8,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
                elevation: 0,
                minimumSize: const Size(double.infinity, 50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: Text(
                'Apply to Mentor',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(height: 30),
          Text(
            'Mentorship Stats',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          _buildStatItem('Active Mentors', '156', Icons.people, gradients[0]),
          _buildStatItem('Sessions This Month', '1,234', Icons.video_call, gradients[1]),
          _buildStatItem('Success Stories', '89%', Icons.trending_up, gradients[2]),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, List<Color> gradient) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: gradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: gradient[0].withAlpha(40),
                  blurRadius: 8,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(icon, color: Colors.white, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.black87,
                  ),
                ),
                Text(
                  label,
                  style: GoogleFonts.poppins(
                    color: Colors.black54,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Wrap content mentor card with no extra space
  Widget _buildCompactMentorCard({
    required String name,
    required String expertise,
    required double rating,
    required int reviews,
    required String imageUrl,
    required List<String> skills,
    bool isTopMentor = false,
  }) {
    // Determine which gradient to use based on name
    final List<Color> mentorGradient = name.contains('Marcus') ? gradients[0] : gradients[1];

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(10),
      child: IntrinsicHeight(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with avatar and name
            Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: mentorGradient,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: mentorGradient[0].withAlpha(40),
                        blurRadius: 3,
                        spreadRadius: 0,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.all(2),
                  child: CircleAvatar(
                    radius: 18,
                    backgroundColor: Colors.grey[300],
                    child: Icon(
                      Icons.person,
                      size: 18,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.bold,
                          fontSize: 13,
                          color: Colors.black87,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        expertise,
                        style: GoogleFonts.poppins(
                          color: Colors.black54,
                          fontSize: 10,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Rating and skills in a row to save space
            const SizedBox(height: 6),
            Row(
              children: [
                // Star rating
                Row(
                  children: List.generate(
                    5,
                    (index) => Icon(
                      index < rating.floor()
                          ? Icons.star
                          : index < rating
                              ? Icons.star_half
                              : Icons.star_border,
                      color: mentorGradient[0],
                      size: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  '$rating',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    color: Colors.black54,
                    fontWeight: FontWeight.w600,
                  ),
                ),

                // Top mentor badge
                if (isTopMentor) ...[
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: mentorGradient,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      'Top',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 9,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),

            // Skills
            const SizedBox(height: 6),
            Wrap(
              spacing: 4,
              runSpacing: 4,
              children: skills.map((skill) => _buildCompactBadge(skill, mentorGradient[0])).toList(),
            ),

            // View profile button
            const SizedBox(height: 6),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: mentorGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: mentorGradient[0].withAlpha(40),
                    blurRadius: 3,
                    spreadRadius: 0,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: TextButton(
                onPressed: () {},
                style: TextButton.styleFrom(
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 6),
                  minimumSize: const Size(double.infinity, 30),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(
                  'View Profile',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Smaller badge for mentor skills
  Widget _buildCompactBadge(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        label,
        style: GoogleFonts.poppins(
          color: color,
          fontSize: 9,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }





  Widget _buildContests() {
    return CustomScrollView(
      controller: _challengesScrollController,
      slivers: [
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Challenges & Contests',
                  style: GoogleFonts.poppins(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ).animate().fadeIn(duration: 400.ms).slideY(begin: 0.2, end: 0, duration: 400.ms),
                const SizedBox(height: 4),
                Text(
                  'Showcase your skills and win exciting prizes',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ).animate().fadeIn(duration: 400.ms, delay: 100.ms).slideY(begin: 0.2, end: 0, duration: 400.ms, delay: 100.ms),
                const SizedBox(height: 16),
                _buildFeaturedChallenge().animate().fadeIn(duration: 500.ms, delay: 200.ms).scale(begin: const Offset(0.95, 0.95), end: const Offset(1, 1), duration: 500.ms, delay: 200.ms),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Active Challenges',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        setState(() {
                          _showFilters = !_showFilters;
                        });
                      },
                      icon: const Icon(Icons.filter_list, size: 18),
                      label: Text(
                        'Filter',
                        style: GoogleFonts.poppins(),
                      ),
                      style: TextButton.styleFrom(
                        foregroundColor: const Color(0xFF4F46E5),
                      ),
                    ),
                  ],
                ).animate().fadeIn(duration: 400.ms, delay: 300.ms),
                const SizedBox(height: 8),

                // Filter Section
                if (_showFilters) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.only(bottom: 16),
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                        Text(
                          'Filter Challenges',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 12),

                        // Category Filter
                        Text(
                          'Category',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey[700],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: [
                            'All',
                            'UI/UX Design',
                            'Code Implementation',
                            'Bug Fix',
                            'Feature Development',
                            'Performance Optimization',
                            'Mobile App Design',
                            'Web Development',
                          ].map((category) => _buildFilterChip(
                            category,
                            _selectedChallengeCategory == category,
                            isCategory: true,
                          )).toList(),
                        ),

                        const SizedBox(height: 16),

                        // Prize Range Filter
                        Text(
                          'Prize Range',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey[700],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: [
                            'All',
                            '\$30-\$100',
                            '\$100-\$500',
                            '\$500-\$1000',
                            '\$1000+',
                          ].map((range) => _buildFilterChip(
                            range,
                            _selectedPrizeRange == range,
                            isCategory: false,
                          )).toList(),
                        ),

                        const SizedBox(height: 16),

                        // Filter Action Buttons
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton(
                                onPressed: () {
                                  setState(() {
                                    _selectedChallengeCategory = 'All';
                                    _selectedPrizeRange = 'All';
                                  });
                                },
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.grey[600],
                                  side: BorderSide(color: Colors.grey[300]!),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: Text(
                                  'Clear',
                                  style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: () {
                                  setState(() {
                                    _showFilters = false;
                                  });
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF4F46E5),
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  elevation: 0,
                                ),
                                child: Text(
                                  'Apply Filters',
                                  style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                                ),
                              ),
                            ),
                          ],
                        ),
                        ],
                      ),
                    ),
                  ).animate().fadeIn(duration: 300.ms).slideY(begin: -0.2, end: 0),
                ],
              ],
            ),
          ),
        ),
        StreamBuilder<List<ChallengeModel>>(
          stream: _challengeService.getActiveChallenges(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4F46E5)),
                    ),
                  ),
                ),
              );
            }

            if (snapshot.hasError) {
              return SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading challenges',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }

            final allChallenges = snapshot.data ?? [];

            // Apply filters
            final challenges = allChallenges.where((challenge) {
              // Category filter
              if (_selectedChallengeCategory != 'All' && challenge.category != _selectedChallengeCategory) {
                return false;
              }

              // Prize range filter
              if (_selectedPrizeRange != 'All') {
                final prizeAmount = challenge.prizeAmount;
                switch (_selectedPrizeRange) {
                  case '\$30-\$100':
                    if (prizeAmount < 30 || prizeAmount > 100) return false;
                    break;
                  case '\$100-\$500':
                    if (prizeAmount < 100 || prizeAmount > 500) return false;
                    break;
                  case '\$500-\$1000':
                    if (prizeAmount < 500 || prizeAmount > 1000) return false;
                    break;
                  case '\$1000+':
                    if (prizeAmount < 1000) return false;
                    break;
                }
              }

              return true;
            }).toList();

            if (challenges.isEmpty) {
              return SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(Icons.emoji_events_outlined, size: 64, color: Colors.grey[400]),
                        const SizedBox(height: 16),
                        Text(
                          'No active challenges',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Be the first to create a challenge!',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }

            return SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final challenge = challenges[index];
                  return _buildRealChallengeCard(challenge, index);
                },
                childCount: challenges.length,
              ),
            );
          },
        ),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                Text(
                  'Your Submissions',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ).animate().fadeIn(duration: 400.ms, delay: 400.ms),
                const SizedBox(height: 16),
                _buildRealSubmissionsCard().animate().fadeIn(duration: 400.ms, delay: 500.ms),
                const SizedBox(height: 24),
                Text(
                  'Leaderboard',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ).animate().fadeIn(duration: 400.ms, delay: 600.ms),
                const SizedBox(height: 16),
                _buildRealLeaderboardCard().animate().fadeIn(duration: 400.ms, delay: 700.ms),
                const SizedBox(height: 24),
                _buildCreateChallengeCard(context).animate().fadeIn(duration: 400.ms, delay: 800.ms),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturedChallenge() {
    return StreamBuilder<List<ChallengeModel>>(
      stream: _challengeService.getFeaturedChallenges(limit: 3),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildFeaturedChallengePlaceholder();
        }

        final challenges = snapshot.data ?? [];
        if (challenges.isEmpty) {
          return _buildNoFeaturedChallenges();
        }

        return _buildFeaturedChallengesSlider(challenges);
      },
    );
  }

  Widget _buildFeaturedChallengePlaceholder() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            // Background image with gradient overlay
            Container(
              height: 280,
              width: double.infinity,
              color: Colors.grey[200],
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.image,
                      size: 50,
                      color: Colors.grey[400],
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withAlpha(180),
                          Colors.black.withAlpha(220),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: const Color(0xFF4F46E5),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            'FEATURED',
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(30),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.timer, color: Colors.white, size: 14),
                              const SizedBox(width: 4),
                              Text(
                                '5 days left',
                                style: GoogleFonts.poppins(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'UI/UX Redesign Challenge',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Create a fresh, innovative UI/UX redesign for the Asatu Marketplace screen. Winners will have their designs implemented in the next app update and receive exclusive badges.',
                      style: GoogleFonts.poppins(
                        color: Colors.white.withAlpha(230),
                        fontSize: 14,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.monetization_on, color: Colors.amber, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              'No Featured Challenge',
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            const Icon(Icons.people, color: Colors.white, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              '87 Entries',
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        ElevatedButton(
                          onPressed: () {},
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: const Color(0xFF4F46E5),
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            'Enter Now',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoFeaturedChallenges() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF6A11CB),
                Color(0xFF2575FC),
                Color(0xFF4F46E5),
              ],
            ),
          ),
          child: Stack(
            children: [
              // Asatu branding pattern - centered
              Positioned(
                top: 20,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(20),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.star_outline,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'ASATU',
                          style: GoogleFonts.poppins(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // Content
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: const Color(0xFF4F46E5),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        'FEATURED',
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Icon(
                      Icons.emoji_events_outlined,
                      color: Colors.white,
                      size: 48,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'No Featured Challenges',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Create a high-value challenge to get featured!',
                      style: GoogleFonts.poppins(
                        color: Colors.white.withAlpha(230),
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturedChallengesSlider(List<ChallengeModel> challenges) {
    return _FeaturedChallengesSlider(challenges: challenges);
  }

  // Build filter chip for challenges
  Widget _buildFilterChip(String label, bool isSelected, {bool isCategory = true}) {
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.poppins(
          color: isSelected ? Colors.white : Colors.black87,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          fontSize: 12,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          if (isCategory) {
            _selectedChallengeCategory = selected ? label : 'All';
          } else {
            _selectedPrizeRange = selected ? label : 'All';
          }
        });
      },
      backgroundColor: Colors.grey[100],
      selectedColor: const Color(0xFF4F46E5),
      checkmarkColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
    );
  }

  // Get category color for challenge badges
  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'ui design':
      case 'ui/ux design':
      case 'ui/ux':
        return Colors.purple;
      case 'code implementation':
      case 'coding':
        return Colors.green;
      case 'bug fix':
        return Colors.red;
      case 'feature development':
        return Colors.blue;
      case 'performance optimization':
        return Colors.orange;
      case 'mobile app design':
        return Colors.indigo;
      case 'web development':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }



  Widget _buildRealChallengeCard(ChallengeModel challenge, int index) {
    final color = _getCategoryColor(challenge.category);

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: Colors.grey[200]!),
        ),
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ChallengeDetailPage(
                  challengeId: challenge.id,
                  challenge: challenge,
                ),
              ),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                      decoration: BoxDecoration(
                        color: color.withAlpha(30),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        challenge.category,
                        style: GoogleFonts.poppins(
                          color: color,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                      decoration: BoxDecoration(
                        color: const Color(0xFF4F46E5).withAlpha(15),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '\$${challenge.prizeAmount.toStringAsFixed(0)}',
                        style: GoogleFonts.poppins(
                          color: const Color(0xFF4F46E5),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  challenge.title,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  challenge.description,
                  style: GoogleFonts.poppins(
                    color: Colors.grey[600],
                    fontSize: 14,
                    height: 1.4,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.people, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          '${challenge.submissionCount} entries',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          '${challenge.daysRemaining} days left',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    OutlinedButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ChallengeDetailPage(
                              challengeId: challenge.id,
                              challenge: challenge,
                            ),
                          ),
                        );
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: const Color(0xFF4F46E5),
                        side: const BorderSide(color: Color(0xFF4F46E5)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                      child: Text(
                        'View',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    ).animate().fadeIn(
          duration: 400.ms,
          delay: Duration(milliseconds: 300 + (index * 100)),
        ).slideY(
          begin: 0.2,
          end: 0,
          duration: 400.ms,
          delay: Duration(milliseconds: 300 + (index * 100)),
        );
  }





  Widget _buildRealSubmissionsCard() {
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;
    if (currentUserId == null) {
      return Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: Colors.grey[200]!),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Text(
              'Please log in to view your submissions',
              style: GoogleFonts.poppins(color: Colors.grey[600]),
            ),
          ),
        ),
      );
    }

    return StreamBuilder<List<ChallengeSubmissionModel>>(
      stream: _challengeService.getUserSubmissions(currentUserId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: Colors.grey[200]!),
            ),
            child: const Padding(
              padding: EdgeInsets.all(40),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        if (snapshot.hasError) {
          return Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: Colors.grey[200]!),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading submissions',
                    style: GoogleFonts.poppins(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          );
        }

        final submissions = snapshot.data ?? [];
        final activeSubmissions = submissions.where((s) => !s.isWinner).length;

        return Card(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.grey[200]!),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Your Submissions',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (activeSubmissions > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.green.withAlpha(30),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          '$activeSubmissions Active',
                          style: GoogleFonts.poppins(
                            color: Colors.green,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                if (submissions.isEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.upload_outlined, size: 48, color: Colors.grey[400]),
                          const SizedBox(height: 16),
                          Text(
                            'No submissions yet',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Participate in challenges to see your submissions here',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  )
                else ...[
                  ...submissions.take(3).map((submission) {
                    final timeAgo = _getTimeAgo(submission.submittedAt);
                    final status = submission.isWinner ? 'Winner' : 'Under Review';
                    final statusColor = submission.isWinner ? Colors.green : Colors.orange;

                    return Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: _buildSubmissionItem(
                        submission.title,
                        timeAgo,
                        status,
                        statusColor,
                        details: submission.isWinner ? 'Congratulations!' : null,
                      ),
                    );
                  }).toList(),
                  if (submissions.length > 3) ...[
                    const SizedBox(height: 16),
                    Center(
                      child: TextButton.icon(
                        onPressed: () {
                          // Navigate to challenge submissions page showing all user submissions
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Viewing all your submissions',
                                style: GoogleFonts.poppins(),
                              ),
                              backgroundColor: const Color(0xFF4F46E5),
                            ),
                          );
                        },
                        icon: const Icon(Icons.visibility_outlined, size: 16),
                        label: Text(
                          'View all submissions (${submissions.length})',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        style: TextButton.styleFrom(
                          foregroundColor: const Color(0xFF4F46E5),
                        ),
                      ),
                    ),
                  ],
                ],
              ],
            ),
          ),
        );
      },
    );
  }



  Widget _buildSubmissionItem(
    String title,
    String time,
    String status,
    Color statusColor, {
    String? details,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(
            color: statusColor,
            width: 4,
          ),
        ),
      ),
      padding: const EdgeInsets.only(left: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Submitted $time',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 6),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: statusColor.withAlpha(30),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  status,
                  style: GoogleFonts.poppins(
                    color: statusColor,
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (details != null) ...[
                const SizedBox(width: 8),
                Text(
                  '($details)',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRealLeaderboardCard() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _getTopEarners(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: Colors.grey[200]!),
            ),
            child: const Padding(
              padding: EdgeInsets.all(40),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        if (snapshot.hasError) {
          return Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: Colors.grey[200]!),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading leaderboard',
                    style: GoogleFonts.poppins(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          );
        }

        final topEarners = snapshot.data ?? [];

        return Card(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.grey[200]!),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Top Earners This Month',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                if (topEarners.isEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.leaderboard_outlined, size: 48, color: Colors.grey[400]),
                          const SizedBox(height: 16),
                          Text(
                            'No earnings data yet',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Complete challenges to appear on the leaderboard',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  )
                else ...[
                  ...topEarners.asMap().entries.map((entry) {
                    final index = entry.key;
                    final user = entry.value;
                    final isCurrentUser = user['uid'] == FirebaseAuth.instance.currentUser?.uid;

                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: _buildLeaderboardItem(
                        user['displayName'] ?? 'Anonymous',
                        '\$${(user['monthlyEarnings'] ?? 0.0).toStringAsFixed(0)}',
                        user['photoURL'] ?? '',
                        position: index + 1,
                        isHighlighted: isCurrentUser,
                        userId: user['uid'],
                      ),
                    );
                  }).toList(),
                  const SizedBox(height: 20),
                  Center(
                    child: TextButton.icon(
                      onPressed: () {
                        // Show full leaderboard
                        _showFullLeaderboard();
                      },
                      icon: const Icon(Icons.leaderboard_outlined, size: 16),
                      label: Text(
                        'View full leaderboard',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      style: TextButton.styleFrom(
                        foregroundColor: const Color(0xFF4F46E5),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Future<List<Map<String, dynamic>>> _getTopEarners() async {
    try {
      // Get users with highest monthly earnings
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);

      // Query transactions for this month to calculate earnings (without complex where to avoid Firebase index)
      final transactionsSnapshot = await FirebaseFirestore.instance
          .collection('transactions')
          .where('type', isEqualTo: 'challenge_win')
          .get();

      // Group by user and calculate total earnings (filter by date in memory)
      final Map<String, double> userEarnings = {};
      for (final doc in transactionsSnapshot.docs) {
        final data = doc.data();
        final userId = data['userId'] as String;
        final amount = (data['amount'] as num?)?.toDouble() ?? 0.0;
        final createdAt = data['createdAt'] as Timestamp?;

        // Filter by current month in memory to avoid Firebase index
        if (createdAt != null) {
          final transactionDate = createdAt.toDate();
          if (transactionDate.isAfter(startOfMonth)) {
            userEarnings[userId] = (userEarnings[userId] ?? 0.0) + amount;
          }
        }
      }

      // Get user details for top earners
      final List<Map<String, dynamic>> topEarners = [];
      final sortedEntries = userEarnings.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      for (final entry in sortedEntries.take(5)) {
        try {
          final userDoc = await FirebaseFirestore.instance
              .collection('users')
              .doc(entry.key)
              .get();

          if (userDoc.exists) {
            final userData = userDoc.data()!;
            topEarners.add({
              'uid': entry.key,
              'displayName': userData['displayName'] ?? 'Anonymous',
              'photoURL': userData['photoURL'] ?? '',
              'monthlyEarnings': entry.value,
            });
          }
        } catch (e) {
          debugPrint('Error fetching user data for ${entry.key}: $e');
        }
      }

      return topEarners;
    } catch (e) {
      debugPrint('Error getting top earners: $e');
      return [];
    }
  }

  void _showFullLeaderboard() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFF6A11CB), Color(0xFF2575FC)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.leaderboard, color: Colors.white, size: 24),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Monthly Leaderboard',
                        style: GoogleFonts.poppins(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),

              // Leaderboard content
              Expanded(
                child: FutureBuilder<List<Map<String, dynamic>>>(
                  future: _getFullLeaderboard(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (snapshot.hasError) {
                      return Center(
                        child: Text(
                          'Error loading leaderboard',
                          style: GoogleFonts.poppins(color: Colors.grey[600]),
                        ),
                      );
                    }

                    final users = snapshot.data ?? [];

                    if (users.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.leaderboard_outlined, size: 64, color: Colors.grey[400]),
                            const SizedBox(height: 16),
                            Text(
                              'No earnings data yet',
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Complete challenges to appear on the leaderboard',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: Colors.grey[500],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.builder(
                      padding: const EdgeInsets.all(20),
                      itemCount: users.length,
                      itemBuilder: (context, index) {
                        final user = users[index];
                        final isCurrentUser = user['uid'] == FirebaseAuth.instance.currentUser?.uid;

                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: _buildLeaderboardItem(
                            user['displayName'] ?? 'Anonymous',
                            '\$${(user['monthlyEarnings'] ?? 0.0).toStringAsFixed(0)}',
                            user['photoURL'] ?? '',
                            position: index + 1,
                            isHighlighted: isCurrentUser,
                            userId: user['uid'],
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<List<Map<String, dynamic>>> _getFullLeaderboard() async {
    try {
      // Get users with highest monthly earnings (top 50)
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);

      // Query transactions for this month to calculate earnings
      final transactionsSnapshot = await FirebaseFirestore.instance
          .collection('transactions')
          .where('type', isEqualTo: 'challenge_win')
          .get();

      // Group by user and calculate total earnings
      final Map<String, double> userEarnings = {};
      for (final doc in transactionsSnapshot.docs) {
        final data = doc.data();
        final userId = data['userId'] as String;
        final amount = (data['amount'] as num?)?.toDouble() ?? 0.0;
        final createdAt = data['createdAt'] as Timestamp?;

        // Filter by current month
        if (createdAt != null) {
          final transactionDate = createdAt.toDate();
          if (transactionDate.isAfter(startOfMonth)) {
            userEarnings[userId] = (userEarnings[userId] ?? 0.0) + amount;
          }
        }
      }

      // Get user details for top earners
      final List<Map<String, dynamic>> topEarners = [];
      final sortedEntries = userEarnings.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      for (final entry in sortedEntries.take(50)) {
        try {
          final userDoc = await FirebaseFirestore.instance
              .collection('users')
              .doc(entry.key)
              .get();

          if (userDoc.exists) {
            final userData = userDoc.data()!;
            topEarners.add({
              'uid': entry.key,
              'displayName': userData['displayName'] ?? 'Anonymous',
              'photoURL': userData['photoURL'] ?? '',
              'monthlyEarnings': entry.value,
            });
          }
        } catch (e) {
          debugPrint('Error fetching user data for ${entry.key}: $e');
        }
      }

      return topEarners;
    } catch (e) {
      debugPrint('Error getting full leaderboard: $e');
      return [];
    }
  }



  Widget _buildLeaderboardItem(
    String name,
    String points,
    String imageUrl, {
    required int position,
    bool isHighlighted = false,
    String? userId,
  }) {
    Color getPositionColor() {
      switch (position) {
        case 1:
          return const Color(0xFFFFD700); // Gold
        case 2:
          return const Color(0xFFC0C0C0); // Silver
        case 3:
          return const Color(0xFFCD7F32); // Bronze
        default:
          return const Color(0xFF4F46E5); // Brand color
      }
    }

    return GestureDetector(
      onTap: userId != null && userId != FirebaseAuth.instance.currentUser?.uid
          ? () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PublicProfilePage(userId: userId),
                ),
              );
            }
          : null,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
        decoration: BoxDecoration(
          color: isHighlighted ? const Color(0xFF4F46E5).withAlpha(10) : null,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                color: getPositionColor(),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: getPositionColor().withAlpha(100),
                    blurRadius: 4,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  position.toString(),
                  style: GoogleFonts.poppins(
                    color: position <= 3 ? Colors.white : Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Container(
                width: 32,
                height: 32,
                color: Colors.grey[300],
                child: Icon(
                  Icons.person,
                  size: 18,
                  color: Colors.grey[600],
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                name,
                style: GoogleFonts.poppins(
                  fontWeight: name == 'You' ? FontWeight.w700 : FontWeight.w500,
                  color: name == 'You' ? const Color(0xFF4F46E5) : null,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: const Color(0xFF4F46E5).withAlpha(15),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                points.startsWith('\$') ? points : '$points earned',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                  color: const Color(0xFF4F46E5),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateChallengeCard(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey[200]!),
      ),
      child: Stack(
        children: [
          Positioned(
            right: -20,
            bottom: -20,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: const Color(0xFF4F46E5).withAlpha(10),
                shape: BoxShape.circle,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: const Color(0xFF4F46E5).withAlpha(15),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.lightbulb_outline,
                        color: Color(0xFF4F46E5),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Create a Challenge',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'Need help with your project? Create a challenge and get designs or solutions from the community. Minimum prize: \$30.',
                  style: GoogleFonts.poppins(
                    color: Colors.grey[600],
                    fontSize: 14,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 20),
                ElevatedButton.icon(
                  onPressed: () {
                    _showCreateChallengeModal(context);
                  },
                  icon: const Icon(Icons.add, size: 18),
                  label: Text(
                    'Create Challenge',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4F46E5),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


}

// Create Challenge Dialog
class CreateChallengeDialog extends StatefulWidget {
  final ChallengeService challengeService;

  const CreateChallengeDialog({
    super.key,
    required this.challengeService,
  });

  @override
  State<CreateChallengeDialog> createState() => _CreateChallengeDialogState();
}

class _CreateChallengeDialogState extends State<CreateChallengeDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _prizeController = TextEditingController();
  final _requirementsController = TextEditingController();
  final _tagsController = TextEditingController();

  String _selectedCategory = 'UI/UX Design';
  int _selectedDays = 7;
  bool _isCreating = false;
  double _walletBalance = 0.0;

  final List<String> _categories = [
    'UI/UX Design',
    'Code Implementation',
    'Bug Fix',
    'Feature Development',
    'Performance Optimization',
    'Mobile App Design',
    'Web Development',
    'Database Design',
    'API Development',
    'Testing & QA',
  ];

  final List<int> _dayOptions = [3, 5, 7, 10, 14, 21, 30];

  @override
  void initState() {
    super.initState();
    _fetchWalletBalance();
  }

  Future<void> _fetchWalletBalance() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        if (userDoc.exists && mounted) {
          setState(() {
            _walletBalance = (userDoc.data()?['balance'] as num?)?.toDouble() ?? 0.0;
          });
        }
      }
    } catch (e) {
      debugPrint('Error fetching wallet balance: $e');
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _prizeController.dispose();
    _requirementsController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.85,
          maxWidth: MediaQuery.of(context).size.width * 0.95,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFF6A11CB), Color(0xFF2575FC)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.emoji_events, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Create Challenge',
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Wallet Balance and Refund Info
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.green.withAlpha(20),
                border: Border(
                  bottom: BorderSide(color: Colors.grey[200]!),
                ),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.account_balance_wallet,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Wallet Balance: \$${_walletBalance.toStringAsFixed(2)}',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.green[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withAlpha(20),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.blue.withAlpha(50)),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.info_outline,
                          color: Colors.blue,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            'If no one participates by the deadline, your full amount will be refunded without any fee deduction.',
                            style: GoogleFonts.poppins(
                              fontSize: 11,
                              color: Colors.blue[800],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Form
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTextField(
                        controller: _titleController,
                        label: 'Challenge Title',
                        hint: 'Enter a descriptive title for your challenge',
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a title';
                          }
                          if (value.trim().length < 10) {
                            return 'Title must be at least 10 characters';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      _buildTextField(
                        controller: _descriptionController,
                        label: 'Description',
                        hint: 'Describe what you need help with...',
                        maxLines: 4,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a description';
                          }
                          if (value.trim().length < 50) {
                            return 'Description must be at least 50 characters';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      Column(
                        children: [
                          _buildDropdown(
                            label: 'Category',
                            value: _selectedCategory,
                            items: _categories,
                            onChanged: (value) {
                              setState(() {
                                _selectedCategory = value!;
                              });
                            },
                          ),
                          const SizedBox(height: 16),
                          _buildDropdown(
                            label: 'Duration (Days)',
                            value: '$_selectedDays days',
                            items: _dayOptions.map((e) => '$e days').toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedDays = int.parse(value!.split(' ')[0]);
                              });
                            },
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      _buildTextField(
                        controller: _prizeController,
                        label: 'Prize Amount (\$)',
                        hint: 'Minimum \$30',
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter prize amount';
                          }
                          final amount = double.tryParse(value);
                          if (amount == null || amount < 30) {
                            return 'Minimum prize amount is \$30';
                          }
                          return null;
                        },
                      ),

                      // Platform fee breakdown
                      ValueListenableBuilder<TextEditingValue>(
                        valueListenable: _prizeController,
                        builder: (context, value, child) {
                          final prizeAmount = double.tryParse(value.text) ?? 0.0;
                          if (prizeAmount >= 30) {
                            final platformFee = prizeAmount * 0.20;
                            final winnerAmount = prizeAmount - platformFee;

                            return Container(
                              margin: const EdgeInsets.only(top: 8),
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.blue.withAlpha(20),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.blue.withAlpha(50)),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      const Icon(
                                        Icons.info_outline,
                                        color: Colors.blue,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        'Prize Breakdown',
                                        style: GoogleFonts.poppins(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.blue[800],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 6),
                                  Text(
                                    'Winner receives: \$${winnerAmount.toStringAsFixed(2)} (80%)',
                                    style: GoogleFonts.poppins(
                                      fontSize: 12,
                                      color: Colors.blue[800],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    'Platform fee: \$${platformFee.toStringAsFixed(2)} (20%)',
                                    style: GoogleFonts.poppins(
                                      fontSize: 12,
                                      color: Colors.blue[800],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),

                      // Featured Challenge Benefits - Always show for amounts >= $30
                      ValueListenableBuilder<TextEditingValue>(
                        valueListenable: _prizeController,
                        builder: (context, value, child) {
                          final prizeAmount = double.tryParse(value.text) ?? 0.0;

                          if (prizeAmount < 30) return const SizedBox.shrink();

                          return FutureBuilder<List<ChallengeModel>>(
                            future: widget.challengeService.getTopThreeChallenges(),
                            builder: (context, snapshot) {
                              final topChallenges = snapshot.data ?? [];

                              return Container(
                                margin: const EdgeInsets.only(top: 8),
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.amber.withAlpha(20),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.amber.withAlpha(50)),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        const Icon(
                                          Icons.star,
                                          color: Colors.amber,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 6),
                                        Text(
                                          'Featured Challenge Benefits',
                                          style: GoogleFonts.poppins(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.amber[800],
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 6),
                                    Text(
                                      'High-value challenges get featured with Asatu branding, higher visibility, and priority placement.',
                                      style: GoogleFonts.poppins(
                                        fontSize: 11,
                                        color: Colors.amber[800],
                                      ),
                                    ),
                                    const SizedBox(height: 6),
                                    Text(
                                      '• Premium placement at top of challenges\n• Asatu branded gradient background\n• Higher developer engagement\n• Priority in search results',
                                      style: GoogleFonts.poppins(
                                        fontSize: 10,
                                        color: Colors.amber[700],
                                      ),
                                    ),
                                    if (topChallenges.isNotEmpty) ...[
                                      const SizedBox(height: 8),
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Colors.amber.withAlpha(30),
                                          borderRadius: BorderRadius.circular(6),
                                        ),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Current Featured Amounts:',
                                              style: GoogleFonts.poppins(
                                                fontSize: 10,
                                                fontWeight: FontWeight.w600,
                                                color: Colors.amber[800],
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              topChallenges.take(3).map((c) => '\$${c.prizeAmount.toStringAsFixed(0)}').join(' • '),
                                              style: GoogleFonts.poppins(
                                                fontSize: 11,
                                                color: Colors.amber[700],
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              prizeAmount >= (topChallenges.isNotEmpty ? topChallenges.last.prizeAmount : 100)
                                                  ? '✨ Your challenge will be FEATURED!'
                                                  : 'Increase amount to get featured status',
                                              style: GoogleFonts.poppins(
                                                fontSize: 10,
                                                color: prizeAmount >= (topChallenges.isNotEmpty ? topChallenges.last.prizeAmount : 100)
                                                    ? Colors.green[700]
                                                    : Colors.amber[600],
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              );
                            },
                          );
                        },
                      ),

                      const SizedBox(height: 16),

                      _buildTextField(
                        controller: _requirementsController,
                        label: 'Requirements (Optional)',
                        hint: 'List specific requirements, separated by commas',
                        maxLines: 3,
                      ),

                      const SizedBox(height: 16),

                      _buildTextField(
                        controller: _tagsController,
                        label: 'Tags (Optional)',
                        hint: 'Add relevant tags, separated by commas',
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Actions
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Cancel',
                        style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isCreating ? null : _createChallenge,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF4F46E5),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: _isCreating
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              'Create Challenge',
                              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          keyboardType: keyboardType,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: GoogleFonts.poppins(color: Colors.grey[400]),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF4F46E5)),
            ),
            filled: true,
            fillColor: Colors.grey[50],
            contentPadding: const EdgeInsets.all(12),
          ),
          style: GoogleFonts.poppins(),
        ),
      ],
    );
  }

  Widget _buildDropdown({
    required String label,
    required String value,
    required List<String> items,
    required void Function(String?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
            color: Colors.grey[50],
          ),
          child: DropdownButtonFormField<String>(
            value: value,
            items: items.map((item) {
              return DropdownMenuItem(
                value: item,
                child: Text(
                  item,
                  style: GoogleFonts.poppins(fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                ),
              );
            }).toList(),
            onChanged: onChanged,
            decoration: const InputDecoration(
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              filled: false,
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            ),
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.black87),
            dropdownColor: Colors.white,
            icon: const Icon(Icons.keyboard_arrow_down, color: Color(0xFF4F46E5)),
            isExpanded: true,
          ),
        ),
      ],
    );
  }

  Future<void> _createChallenge() async {
    if (!_formKey.currentState!.validate()) return;

    final prizeAmount = double.tryParse(_prizeController.text.trim());
    if (prizeAmount == null || prizeAmount < 30) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Prize amount must be at least \$30', style: GoogleFonts.poppins()),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (prizeAmount > _walletBalance) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Insufficient balance. You have \$${_walletBalance.toStringAsFixed(2)}', style: GoogleFonts.poppins()),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Check if amount qualifies for featured section and show alert
    final willBeFeatured = await widget.challengeService.isAmountFeatured(prizeAmount);
    if (willBeFeatured) {
      final shouldContinue = await _showFeaturedChallengeAlert(prizeAmount);
      if (!shouldContinue) return;
    }

    setState(() {
      _isCreating = true;
    });

    try {
      final requirements = _requirementsController.text.trim().isNotEmpty
          ? _requirementsController.text.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList()
          : <String>[];

      final tags = _tagsController.text.trim().isNotEmpty
          ? _tagsController.text.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList()
          : <String>[];

      await widget.challengeService.createChallenge(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _selectedCategory,
        prizeAmount: double.parse(_prizeController.text.trim()),
        deadline: DateTime.now().add(Duration(days: _selectedDays)),
        requirements: requirements,
        tags: tags,
      );

      if (mounted) {
        final prizeAmount = double.parse(_prizeController.text.trim());

        // Refresh wallet balance immediately
        await _fetchWalletBalance();

        if (mounted) {
          Navigator.pop(context);
        }
        // Force refresh the challenges list
        setState(() {
          // This will trigger a rebuild and refresh the StreamBuilder
        });
        // Show platform fee alert after a short delay to ensure context is valid
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _showPlatformFeeAlert(prizeAmount);
          }
        });
      }
    } catch (e) {
      if (mounted) {
        final errorMessage = e.toString();
        if (errorMessage.contains('INSUFFICIENT_BALANCE|')) {
          // Extract the balance details from the error message
          final balanceInfo = errorMessage.split('|')[1];
          _showInsufficientBalanceDialog(balanceInfo);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Error creating challenge: $e',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }

  Future<bool> _showFeaturedChallengeAlert(double prizeAmount) async {
    // Get top 3 challenges for reference
    final topChallenges = await widget.challengeService.getTopThreeChallenges();

    if (!mounted) return false;

    return await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.amber.withAlpha(20),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Featured Challenge!',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: Colors.amber[700],
                  ),
                ),
              ),
            ],
          ),
          content: SizedBox(
            width: MediaQuery.of(context).size.width * 0.9,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
              Text(
                'Great news! Your challenge amount of \$${prizeAmount.toStringAsFixed(0)} qualifies for the Featured Challenges section.',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withAlpha(20),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withAlpha(50)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.trending_up,
                          color: Colors.blue,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Featured Benefits',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.blue[800],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• Higher visibility to developers\n• Priority placement in challenge list\n• Increased participation rates\n• Asatu branding and promotion',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.blue[800],
                      ),
                    ),
                  ],
                ),
              ),
              if (topChallenges.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text(
                  'Current Top Challenges:',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                ...topChallenges.take(3).map((challenge) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Container(
                        width: 6,
                        height: 6,
                        decoration: const BoxDecoration(
                          color: Colors.grey,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '${challenge.title} - \$${challenge.prizeAmount.toStringAsFixed(0)}',
                          style: GoogleFonts.poppins(
                            fontSize: 13,
                            color: Colors.grey[600],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                )).toList(),
              ],
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(
                  color: Colors.grey[600],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Create Featured Challenge',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    ) ?? false;
  }

  void _showInsufficientBalanceDialog(String balanceInfo) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(20),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.account_balance_wallet_outlined,
                  color: Colors.red,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Insufficient Balance',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: Colors.red[700],
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                balanceInfo,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withAlpha(20),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withAlpha(50)),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.info_outline,
                      color: Colors.blue,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Add funds to your wallet to create challenges',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.blue[800],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(
                  color: Colors.grey[600],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // Navigate to wallet/add funds page
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Add funds feature coming soon!',
                      style: GoogleFonts.poppins(),
                    ),
                    backgroundColor: const Color(0xFF4F46E5),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4F46E5),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Add Funds',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showPlatformFeeAlert(double prizeAmount) {
    final platformFee = prizeAmount * 0.20; // 20% platform fee
    final winnerAmount = prizeAmount - platformFee;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withAlpha(20),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check_circle_outline,
                  color: Colors.green,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Challenge Created!',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: Colors.green[700],
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Your challenge has been created successfully!',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withAlpha(20),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withAlpha(50)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.info_outline,
                          color: Colors.blue,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Prize Distribution',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.blue[800],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Total Prize: \$${prizeAmount.toStringAsFixed(2)}',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.blue[800],
                      ),
                    ),
                    Text(
                      'Winner receives: \$${winnerAmount.toStringAsFixed(2)} (80%)',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.blue[800],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      'Platform fee: \$${platformFee.toStringAsFixed(2)} (20%)',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.blue[800],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4F46E5),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Got it!',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

// Separate widget for comments bottom sheet to prevent rebuilds
class CommentsBottomSheetWidget extends StatefulWidget {
  final Map<String, dynamic> post;
  final List<Color> commentGradient;
  final Function(int) onCommentAdded;

  const CommentsBottomSheetWidget({
    super.key,
    required this.post,
    required this.commentGradient,
    required this.onCommentAdded,
  });

  @override
  State<CommentsBottomSheetWidget> createState() => _CommentsBottomSheetWidgetState();
}

class _CommentsBottomSheetWidgetState extends State<CommentsBottomSheetWidget> {
  final TextEditingController _commentController = TextEditingController();
  final PostUpdateService _postUpdateService = PostUpdateService();
  List<Map<String, dynamic>> _comments = [];
  bool _isLoading = true;

  // Debounce mechanism for comment likes
  final Set<String> _likingComments = <String>{};

  @override
  void initState() {
    super.initState();
    _loadComments();
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  // Get current user image from Firebase or Google profile
  Future<String?> _getCurrentUserImage() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return null;

      // First try Firebase Auth photoURL (Google profile image)
      if (currentUser.photoURL != null && currentUser.photoURL!.isNotEmpty) {
        return currentUser.photoURL;
      }

      // Try to get user data from Firestore
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final firestoreImage = userData['photoURL'];
        if (firestoreImage != null && firestoreImage.toString().isNotEmpty) {
          return firestoreImage;
        }
      }

      // Generate Google profile image from email if available
      if (currentUser.email != null) {
        // Use Google's profile image API based on email
        final email = currentUser.email!;
        return 'https://ui-avatars.com/api/?name=${Uri.encodeComponent(email)}&background=4F46E5&color=fff&size=128';
      }

      return null;
    } catch (e) {
      debugPrint('Error fetching current user image: $e');
      return FirebaseAuth.instance.currentUser?.photoURL;
    }
  }

  // Show reply dialog for a comment
  void _showReplyDialog(Map<String, dynamic> comment) {
    // Use the unique username field, not the display name
    final String username = comment['username'] ?? comment['userName'] ?? comment['authorName'] ?? 'User';
    final String displayName = comment['userName'] ?? comment['authorName'] ?? username;

    // Set the comment input to mention the user using their unique username
    _commentController.text = '@$username ';
    _commentController.selection = TextSelection.fromPosition(
      TextPosition(offset: _commentController.text.length),
    );

    // Show a brief message with display name but mention with username
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Replying to $displayName (@$username)',
          style: GoogleFonts.poppins(),
        ),
        backgroundColor: widget.commentGradient[0],
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }



  Future<void> _loadComments() async {
    try {
      debugPrint('CommentsBottomSheet: Starting to load comments for post ${widget.post['id']}');
      debugPrint('CommentsBottomSheet: Post data: ${widget.post}');

      // Test: Check if comments collection exists at all
      try {
        final testSnapshot = await FirebaseFirestore.instance.collection('comments').limit(1).get();
        debugPrint('CommentsBottomSheet: Comments collection exists: ${testSnapshot.docs.isNotEmpty}');
        if (testSnapshot.docs.isNotEmpty) {
          final sampleComment = testSnapshot.docs.first.data();
          debugPrint('CommentsBottomSheet: Sample comment structure: ${sampleComment.keys.join(', ')}');
        }
      } catch (e) {
        debugPrint('CommentsBottomSheet: Error checking comments collection: $e');
      }

      if (!mounted) return;

      final userContentService = Provider.of<UserContentService>(context, listen: false);
      final comments = await userContentService.getPostComments(widget.post['id']);

      debugPrint('CommentsBottomSheet: Loaded ${comments.length} comments from UserContentService');

      // Process comments quickly - set basic properties first
      for (int i = 0; i < comments.length; i++) {
        final comment = comments[i];

        try {
          // Set basic properties first
          comment['likes'] = comment['likesCount'] ?? 0;
          comment['isLiked'] = false; // Default to false, will be updated later
        } catch (e) {
          debugPrint('Error processing comment $i: $e');
        }
      }

      if (mounted) {
        setState(() {
          _comments = comments;
          _isLoading = false;
        });
        debugPrint('CommentsBottomSheet: Updated UI with ${_comments.length} comments');

        // Load like statuses in background after UI is updated
        _loadCommentLikeStatuses();
      }
    } catch (e) {
      debugPrint('CommentsBottomSheet: Error loading comments: $e');
      debugPrint('CommentsBottomSheet: Error stack trace: ${StackTrace.current}');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.75,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.only(top: 10),
            width: 40,
            height: 5,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ShaderMask(
                  shaderCallback: (bounds) => LinearGradient(
                    colors: widget.commentGradient,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ).createShader(bounds),
                  child: Text(
                    'Comments',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: Icon(Icons.close, color: Colors.grey[800]),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
              ],
            ),
          ),
          Divider(color: Colors.grey[200]),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _comments.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.chat_bubble_outline,
                              size: 48,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No comments yet',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Be the first to comment!',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        physics: const BouncingScrollPhysics(),
                        itemCount: _comments.length,
                        itemBuilder: (context, index) {
                          final comment = _comments[index];
                          return _buildCommentItem(comment);
                        },
                      ),
          ),
          Divider(color: Colors.grey[200]),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: widget.commentGradient,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: widget.commentGradient[0].withAlpha(50),
                        blurRadius: 8,
                        spreadRadius: 0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.all(2),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                      child: FutureBuilder<String?>(
                        future: _getCurrentUserImage(),
                        builder: (context, snapshot) {
                          final userImage = snapshot.data;
                          return userImage != null && userImage.isNotEmpty
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(18),
                                  child: Image.network(
                                    userImage,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Icon(
                                        Icons.person,
                                        size: 20,
                                        color: Colors.grey[600],
                                      );
                                    },
                                  ),
                                )
                              : Icon(
                                  Icons.person,
                                  size: 20,
                                  color: Colors.grey[600],
                                );
                        },
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _commentController,
                    style: GoogleFonts.poppins(color: Colors.black87),
                    decoration: InputDecoration(
                      hintText: 'Add a comment...',
                      hintStyle: GoogleFonts.poppins(color: Colors.grey[500]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide(color: Colors.grey[300]!),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide(color: Colors.grey[300]!),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide(color: widget.commentGradient[0]),
                      ),
                      filled: true,
                      fillColor: Colors.grey[100],
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: widget.commentGradient,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: widget.commentGradient[0].withAlpha(50),
                        blurRadius: 8,
                        spreadRadius: 0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.send, color: Colors.white, size: 20),
                    onPressed: _addComment,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentItem(Map<String, dynamic> comment) {
    // Format the timestamp
    String timeAgo = 'Just now';
    if (comment['createdAt'] != null) {
      try {
        final DateTime commentTime = (comment['createdAt'] as Timestamp).toDate();
        final Duration difference = DateTime.now().difference(commentTime);

        if (difference.inDays > 0) {
          timeAgo = '${difference.inDays}d ago';
        } else if (difference.inHours > 0) {
          timeAgo = '${difference.inHours}h ago';
        } else if (difference.inMinutes > 0) {
          timeAgo = '${difference.inMinutes}m ago';
        } else {
          timeAgo = 'Just now';
        }
      } catch (e) {
        debugPrint('Error formatting comment timestamp: $e');
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User avatar with gradient border
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: widget.commentGradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: widget.commentGradient[0].withAlpha(50),
                  blurRadius: 8,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            padding: const EdgeInsets.all(2),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  shape: BoxShape.circle,
                ),
                child: (comment['userImage'] ?? comment['authorImage']) != null &&
                       (comment['userImage'] ?? comment['authorImage']).toString().isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(18),
                        child: Image.network(
                          comment['userImage'] ?? comment['authorImage'],
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.person,
                              size: 20,
                              color: Colors.grey[600],
                            );
                          },
                        ),
                      )
                    : Icon(
                        Icons.person,
                        size: 20,
                        color: Colors.grey[600],
                      ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.grey[200]!,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Author name and time
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          comment['userName'] ?? comment['authorName'] ?? 'Anonymous',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                      Text(
                        timeAgo,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  // Comment content with clickable mentions
                  MentionText(
                    text: comment['content'] ?? '',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.black87,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Action buttons
                  Row(
                    children: [
                      InkWell(
                        onTap: () async {
                          // Like functionality
                          final commentId = comment['id'];

                          // Prevent multiple taps
                          if (_likingComments.contains(commentId)) {
                            return;
                          }

                          _likingComments.add(commentId);

                          try {
                            // Use optimized service for ultra-fast comment liking
                            final userContentService = Provider.of<UserContentService>(context, listen: false);
                            final bool isLiked = await userContentService.toggleCommentLike(commentId);

                            if (mounted) {
                              setState(() {
                                comment['isLiked'] = isLiked;
                                // Fix negative count issue
                                final currentLikes = comment['likes'] ?? 0;
                                if (isLiked) {
                                  comment['likes'] = currentLikes + 1;
                                } else {
                                  comment['likes'] = (currentLikes > 0) ? currentLikes - 1 : 0;
                                }
                              });

                              // Broadcast the comment like update to other pages
                              _postUpdateService.updateCommentLike(
                                widget.post['id'],
                                commentId,
                                isLiked,
                                comment['likes']
                              );

                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    isLiked ? 'Comment liked!' : 'Comment unliked!',
                                    style: GoogleFonts.poppins(),
                                  ),
                                  backgroundColor: widget.commentGradient[0],
                                  behavior: SnackBarBehavior.floating,
                                  duration: const Duration(seconds: 1),
                                ),
                              );
                            }
                          } catch (e) {
                            debugPrint('Error toggling comment like: $e');
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Error liking comment',
                                    style: GoogleFonts.poppins(),
                                  ),
                                  backgroundColor: Colors.red,
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                            }
                          } finally {
                            // Remove from debounce set after a delay
                            Future.delayed(const Duration(milliseconds: 1000), () {
                              _likingComments.remove(commentId);
                            });
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                comment['isLiked'] == true ? Icons.favorite : Icons.favorite_border,
                                size: 16,
                                color: comment['isLiked'] == true ? Colors.red : Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${comment['likes'] ?? 0}',
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: comment['isLiked'] == true ? Colors.red : Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      InkWell(
                        onTap: () {
                          // Reply functionality - show reply dialog
                          _showReplyDialog(comment);
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.reply,
                                size: 16,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Reply',
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Load comment like statuses in background
  Future<void> _loadCommentLikeStatuses() async {
    if (!mounted) return;

    try {
      final userContentService = Provider.of<UserContentService>(context, listen: false);

      for (int i = 0; i < _comments.length; i++) {
        if (!mounted) break;

        final comment = _comments[i];
        try {
          final isLiked = await userContentService.hasLikedComment(comment['id']);

          if (mounted) {
            setState(() {
              _comments[i]['isLiked'] = isLiked;
            });
          }
        } catch (e) {
          debugPrint('Error checking like status for comment ${comment['id']}: $e');
        }
      }
    } catch (e) {
      debugPrint('Error loading comment like statuses: $e');
    }
  }

  Future<void> _addComment() async {
    final String commentText = _commentController.text.trim();

    if (commentText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Please enter a comment',
            style: GoogleFonts.poppins(),
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    try {
      final userContentService = Provider.of<UserContentService>(context, listen: false);
      final result = await userContentService.addComment(widget.post['id'], commentText);

      if (result['success']) {
        _commentController.clear();

        // Get current user info for the comment
        final currentUser = FirebaseAuth.instance.currentUser;
        String authorName = 'You';
        String authorImage = '';
        String currentUsername = 'user';

        if (currentUser != null) {
          try {
            // Get user name
            authorName = currentUser.displayName ?? currentUser.email?.split('@')[0] ?? 'You';

            // Get user image and username from Firestore
            final userDoc = await FirebaseFirestore.instance
                .collection('users')
                .doc(currentUser.uid)
                .get();

            if (userDoc.exists) {
              final userData = userDoc.data() as Map<String, dynamic>;
              authorImage = userData['photoURL'] ?? '';
              currentUsername = userData['username'] ?? 'user'; // Get unique username
            }

            // Get user image - prioritize Google profile image
            if (currentUser.photoURL != null && currentUser.photoURL!.isNotEmpty) {
              authorImage = currentUser.photoURL!;
            } else if (authorImage.isEmpty && currentUser.email != null) {
              // If still no image, generate from email
              authorImage = 'https://ui-avatars.com/api/?name=${Uri.encodeComponent(currentUser.email!)}&background=4F46E5&color=fff&size=128';
            }
          } catch (e) {
            debugPrint('Error fetching user data for comment: $e');
            // Fallback
            authorName = currentUser.displayName ?? currentUser.email?.split('@')[0] ?? 'You';
            authorImage = currentUser.photoURL ?? '';
            if (authorImage.isEmpty && currentUser.email != null) {
              authorImage = 'https://ui-avatars.com/api/?name=${Uri.encodeComponent(currentUser.email!)}&background=4F46E5&color=fff&size=128';
            }
          }
        }

        // Add the new comment to the local list
        setState(() {
          _comments.add({
            'id': 'temp_${DateTime.now().millisecondsSinceEpoch}',
            'content': commentText,
            'userName': authorName,
            'username': currentUsername, // Add unique username field
            'userImage': authorImage,
            'createdAt': Timestamp.now(),
          });
        });

        // Update parent - increment the existing comment count
        final currentCount = widget.post['comments'] ?? widget.post['commentsCount'] ?? 0;
        widget.onCommentAdded(currentCount + 1);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Comment added successfully!',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: widget.commentGradient[0],
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to add comment: ${result['error']}',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error adding comment: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}

// Featured Challenges Slider Widget with Auto-slide and Dots
class _FeaturedChallengesSlider extends StatefulWidget {
  final List<ChallengeModel> challenges;

  const _FeaturedChallengesSlider({required this.challenges});

  @override
  State<_FeaturedChallengesSlider> createState() => _FeaturedChallengesSliderState();
}

class _FeaturedChallengesSliderState extends State<_FeaturedChallengesSlider> {
  late PageController _pageController;
  int _currentPage = 0;
  Timer? _autoSlideTimer;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _startAutoSlide();
  }

  @override
  void dispose() {
    _autoSlideTimer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoSlide() {
    if (widget.challenges.length <= 1) return;

    _autoSlideTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted && _pageController.hasClients) {
        final nextPage = (_currentPage + 1) % widget.challenges.length;
        _pageController.animateToPage(
          nextPage,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Slider
        SizedBox(
          height: 200, // Reduced height
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.challenges.length,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: _buildFeaturedChallengeCard(widget.challenges[index]),
              );
            },
          ),
        ),

        // Dot Indicators
        if (widget.challenges.length > 1) ...[
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              widget.challenges.length,
              (index) => Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                width: _currentPage == index ? 24 : 8,
                height: 8,
                decoration: BoxDecoration(
                  color: _currentPage == index
                      ? const Color(0xFF4F46E5)
                      : Colors.grey[300],
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildFeaturedChallengeCard(ChallengeModel challenge) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            // Background with Asatu branding gradient
            Container(
              height: 200, // Reduced height
              width: double.infinity,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF6A11CB),
                    Color(0xFF2575FC),
                    Color(0xFF4F46E5),
                  ],
                ),
              ),
              child: Stack(
                children: [
                  // Asatu branding pattern - positioned at top left
                  Positioned(
                    top: 12,
                    left: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(25),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.star,
                            color: Colors.white,
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'ASATU',
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontSize: 9,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 1.0,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Gradient overlay
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withAlpha(100),
                          Colors.black.withAlpha(180),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: const Color(0xFF4F46E5),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'FEATURED',
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 10,
                            ),
                          ),
                        ),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(30),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.timer, color: Colors.white, size: 12),
                              const SizedBox(width: 4),
                              Text(
                                '${challenge.daysRemaining} days left',
                                style: GoogleFonts.poppins(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 10,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      challenge.title,
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      challenge.description,
                      style: GoogleFonts.poppins(
                        color: Colors.white.withAlpha(230),
                        fontSize: 12,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.monetization_on, color: Colors.amber, size: 14),
                            const SizedBox(width: 4),
                            Text(
                              '\$${challenge.prizeAmount.toStringAsFixed(0)} Prize',
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            const Icon(Icons.people, color: Colors.white, size: 14),
                            const SizedBox(width: 4),
                            Text(
                              '${challenge.submissionCount}',
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                        ElevatedButton(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ChallengeDetailPage(
                                  challengeId: challenge.id,
                                  challenge: challenge,
                                ),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: const Color(0xFF4F46E5),
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 0,
                            minimumSize: Size.zero,
                          ),
                          child: Text(
                            'Enter',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}