import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// High-performance data loading service with caching, batching, and optimization
class DataPerformanceService {
  static final DataPerformanceService _instance = DataPerformanceService._internal();
  factory DataPerformanceService() => _instance;
  DataPerformanceService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Cache management
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Map<String, Timer> _cacheTimers = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);
  static const Duration _shortCacheExpiry = Duration(minutes: 1);

  // Batch operations
  final Map<String, List<Completer<dynamic>>> _pendingRequests = {};
  final Map<String, Timer> _batchTimers = {};
  static const Duration _batchDelay = Duration(milliseconds: 50);

  // Connection pooling
  final Queue<DocumentReference> _documentPool = Queue<DocumentReference>();
  final Queue<Query> _queryPool = Queue<Query>();

  String? get currentUserId => _auth.currentUser?.uid;

  /// Initialize performance optimizations
  void initialize() {
    try {
      // Enable Firestore offline persistence with optimized settings
      _firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
        ignoreUndefinedProperties: true, // Ignore unknown fields for faster parsing
      );

      // Enable network optimizations
      _firestore.enableNetwork();

      // Clear any existing cache on startup for fresh data
      _firestore.clearPersistence().catchError((e) {
        debugPrint('DataPerformanceService: Cache clear failed (expected on first run): $e');
      });

      debugPrint('DataPerformanceService: Initialized with ultra-fast caching and batching');
    } catch (e) {
      debugPrint('DataPerformanceService: Initialization error (continuing anyway): $e');
    }
  }

  /// Get cached data or fetch from Firestore
  Future<T?> getCached<T>(String key, Future<T> Function() fetcher, {Duration? expiry}) async {
    final cacheKey = '${T.toString()}_$key';
    final now = DateTime.now();
    final cacheExpiry = expiry ?? _cacheExpiry;

    // Check if cached data exists and is still valid
    if (_cache.containsKey(cacheKey) && _cacheTimestamps.containsKey(cacheKey)) {
      final cacheTime = _cacheTimestamps[cacheKey]!;
      if (now.difference(cacheTime) < cacheExpiry) {
        debugPrint('DataPerformanceService: Cache hit for $cacheKey');
        return _cache[cacheKey] as T?;
      }
    }

    // Fetch new data
    try {
      final data = await fetcher();
      _cache[cacheKey] = data;
      _cacheTimestamps[cacheKey] = now;

      // Set cache expiry timer
      _cacheTimers[cacheKey]?.cancel();
      _cacheTimers[cacheKey] = Timer(cacheExpiry, () {
        _cache.remove(cacheKey);
        _cacheTimestamps.remove(cacheKey);
        _cacheTimers.remove(cacheKey);
      });

      debugPrint('DataPerformanceService: Cached new data for $cacheKey');
      return data;
    } catch (e) {
      debugPrint('DataPerformanceService: Error fetching data for $cacheKey: $e');
      return null;
    }
  }

  /// Batch multiple requests together
  Future<T> batchRequest<T>(String batchKey, Future<T> Function() request) async {
    final completer = Completer<T>();

    // Add to pending requests
    _pendingRequests.putIfAbsent(batchKey, () => []);
    _pendingRequests[batchKey]!.add(completer as Completer<dynamic>);

    // Set batch timer if not already set
    if (!_batchTimers.containsKey(batchKey)) {
      _batchTimers[batchKey] = Timer(_batchDelay, () async {
        final requests = _pendingRequests.remove(batchKey) ?? [];
        _batchTimers.remove(batchKey);

        try {
          final result = await request();
          for (final req in requests) {
            if (!req.isCompleted) {
              req.complete(result);
            }
          }
        } catch (e) {
          for (final req in requests) {
            if (!req.isCompleted) {
              req.completeError(e);
            }
          }
        }
      });
    }

    return completer.future;
  }

  /// Fast post like status check with caching
  Future<bool> isPostLiked(String postId) async {
    if (currentUserId == null) return false;

    final cacheKey = 'like_${currentUserId}_$postId';
    return await getCached(
      cacheKey,
      () async {
        final likeId = '${currentUserId}_$postId';
        final doc = await _firestore.collection('post_likes').doc(likeId).get();
        return doc.exists;
      },
      expiry: _shortCacheExpiry,
    ) ?? false;
  }

  /// Fast comment like status check with caching
  Future<bool> isCommentLiked(String commentId) async {
    if (currentUserId == null) return false;

    final cacheKey = 'comment_like_${currentUserId}_$commentId';
    return await getCached(
      cacheKey,
      () async {
        final likeId = '${currentUserId}_$commentId';
        final doc = await _firestore.collection('comment_likes').doc(likeId).get();
        return doc.exists;
      },
      expiry: _shortCacheExpiry,
    ) ?? false;
  }

  /// Optimized post toggle with immediate UI update
  Future<bool> togglePostLike(String postId) async {
    if (currentUserId == null) return false;

    final likeId = '${currentUserId}_$postId';
    final cacheKey = 'like_${currentUserId}_$postId';

    try {
      // Get current status from cache first
      bool isCurrentlyLiked = _cache[cacheKey] ?? false;

      // Optimistically update cache
      _cache[cacheKey] = !isCurrentlyLiked;
      _cacheTimestamps[cacheKey] = DateTime.now();

      // Perform actual toggle in background
      final result = await _firestore.runTransaction<bool>((transaction) async {
        final likeRef = _firestore.collection('post_likes').doc(likeId);
        final postRef = _firestore.collection('posts').doc(postId);

        final likeDoc = await transaction.get(likeRef);
        final postDoc = await transaction.get(postRef);

        if (!postDoc.exists) throw Exception('Post not found');

        final postData = postDoc.data();
        final currentLikes = postData?['likesCount'] ?? 0;

        if (likeDoc.exists) {
          // Unlike
          transaction.delete(likeRef);
          transaction.update(postRef, {'likesCount': currentLikes > 0 ? currentLikes - 1 : 0});
          return false;
        } else {
          // Like
          transaction.set(likeRef, {
            'userId': currentUserId,
            'postId': postId,
            'createdAt': FieldValue.serverTimestamp(),
          });
          transaction.update(postRef, {'likesCount': currentLikes + 1});
          return true;
        }
      });

      // Update cache with actual result
      _cache[cacheKey] = result;
      return result;
    } catch (e) {
      debugPrint('Error toggling post like: $e');
      // Revert optimistic update on error
      final isCurrentlyLiked = await isPostLiked(postId);
      _cache[cacheKey] = isCurrentlyLiked;
      return isCurrentlyLiked;
    }
  }

  /// Optimized comment toggle with immediate UI update
  Future<bool> toggleCommentLike(String commentId) async {
    if (currentUserId == null) return false;

    final likeId = '${currentUserId}_$commentId';
    final cacheKey = 'comment_like_${currentUserId}_$commentId';

    try {
      // Get current status from cache first
      bool isCurrentlyLiked = _cache[cacheKey] ?? false;

      // Optimistically update cache
      _cache[cacheKey] = !isCurrentlyLiked;
      _cacheTimestamps[cacheKey] = DateTime.now();

      // Perform actual toggle in background
      final result = await _firestore.runTransaction<bool>((transaction) async {
        final likeRef = _firestore.collection('comment_likes').doc(likeId);
        final commentRef = _firestore.collection('comments').doc(commentId);

        final likeDoc = await transaction.get(likeRef);
        final commentDoc = await transaction.get(commentRef);

        if (!commentDoc.exists) throw Exception('Comment not found');

        final commentData = commentDoc.data();
        final currentLikes = commentData?['likesCount'] ?? 0;

        if (likeDoc.exists) {
          // Unlike
          transaction.delete(likeRef);
          transaction.update(commentRef, {'likesCount': currentLikes > 0 ? currentLikes - 1 : 0});
          return false;
        } else {
          // Like
          transaction.set(likeRef, {
            'userId': currentUserId,
            'commentId': commentId,
            'createdAt': FieldValue.serverTimestamp(),
          });
          transaction.update(commentRef, {'likesCount': currentLikes + 1});
          return true;
        }
      });

      // Update cache with actual result
      _cache[cacheKey] = result;
      return result;
    } catch (e) {
      debugPrint('Error toggling comment like: $e');
      // Revert optimistic update on error
      final isCurrentlyLiked = await isCommentLiked(commentId);
      _cache[cacheKey] = isCurrentlyLiked;
      return isCurrentlyLiked;
    }
  }

  /// Fast comments loading with caching and NO INDEXES REQUIRED
  Future<List<Map<String, dynamic>>> getPostComments(String postId, {int limit = 50}) async {
    final cacheKey = 'comments_$postId';

    return await getCached(
      cacheKey,
      () async {
        // Use simple query without orderBy to avoid index requirements
        final snapshot = await _firestore
            .collection('comments')
            .where('postId', isEqualTo: postId)
            .limit(limit)
            .get();

        final comments = <Map<String, dynamic>>[];
        for (final doc in snapshot.docs) {
          final data = doc.data();
          final createdAt = data['createdAt'] as Timestamp? ?? Timestamp.now();

          comments.add({
            'id': doc.id,
            'content': data['content'] ?? '',
            'userName': data['userName'] ?? data['authorName'] ?? 'Anonymous',
            'userImage': data['userImage'] ?? data['authorImage'] ?? '',
            'userId': data['userId'] ?? '',
            'postId': data['postId'] ?? '',
            'likesCount': data['likesCount'] ?? 0,
            'likes': data['likesCount'] ?? 0,
            'createdAt': createdAt,
            'isLiked': false, // Will be loaded separately
            'timestamp': createdAt.millisecondsSinceEpoch, // For sorting
          });
        }

        // Sort manually by timestamp (newest first) to avoid Firebase index
        comments.sort((a, b) => (b['timestamp'] as int).compareTo(a['timestamp'] as int));

        // Load like statuses in background
        _loadCommentLikeStatuses(comments);

        return comments;
      },
      expiry: _shortCacheExpiry,
    ) ?? [];
  }

  /// Load comment like statuses in background
  void _loadCommentLikeStatuses(List<Map<String, dynamic>> comments) async {
    if (currentUserId == null) return;

    for (final comment in comments) {
      final commentId = comment['id'];
      isCommentLiked(commentId).then((isLiked) {
        comment['isLiked'] = isLiked;
      });
    }
  }

  /// Clear cache for specific keys
  void clearCache([String? pattern]) {
    if (pattern == null) {
      _cache.clear();
      _cacheTimestamps.clear();
      for (final timer in _cacheTimers.values) {
        timer.cancel();
      }
      _cacheTimers.clear();
    } else {
      final keysToRemove = _cache.keys.where((key) => key.contains(pattern)).toList();
      for (final key in keysToRemove) {
        _cache.remove(key);
        _cacheTimestamps.remove(key);
        _cacheTimers[key]?.cancel();
        _cacheTimers.remove(key);
      }
    }
  }

  /// Preload data for better performance
  void preloadData(String userId) {
    // Preload user's like statuses for recent posts
    _firestore
        .collection('posts')
        .orderBy('createdAt', descending: true)
        .limit(20)
        .get()
        .then((snapshot) {
      for (final doc in snapshot.docs) {
        isPostLiked(doc.id); // This will cache the result
      }
    });
  }

  /// Dispose resources
  void dispose() {
    for (final timer in _cacheTimers.values) {
      timer.cancel();
    }
    for (final timer in _batchTimers.values) {
      timer.cancel();
    }
    _cache.clear();
    _cacheTimestamps.clear();
    _cacheTimers.clear();
    _pendingRequests.clear();
    _batchTimers.clear();
  }
}
