import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'data_performance_service.dart';

/// Service to handle user content like assets, posts, and social connections
class UserContentService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final DataPerformanceService _performanceService = DataPerformanceService();

  // Get current user ID
  String? get currentUserId => _auth.currentUser?.uid;

  // Get user assets
  Future<List<Map<String, dynamic>>> getUserAssets({String? userId}) async {
    try {
      final String targetUserId = userId ?? currentUserId ?? '';
      if (targetUserId.isEmpty) {
        return [];
      }

      final QuerySnapshot snapshot = await _firestore
          .collection('assets')
          .where('authorId', isEqualTo: targetUserId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          'name': data['title'] ?? 'Untitled',
          'type': data['type'] ?? 'Unknown',
          'price': (data['price'] as num?)?.toDouble() ?? 0.0,
          'downloads': (data['downloads'] as num?)?.toInt() ?? 0,
          'rating': (data['rating'] as num?)?.toDouble() ?? 0.0,
          'image': data['thumbnailUrl'] ?? '',
          'authorId': data['authorId'] ?? '',
          'createdAt': (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        };
      }).toList();
    } catch (e) {
      debugPrint('Error getting user assets: $e');
      return [];
    }
  }

  // Get user posts
  Future<List<Map<String, dynamic>>> getUserPosts({String? userId}) async {
    try {
      final String targetUserId = userId ?? currentUserId ?? '';
      if (targetUserId.isEmpty) {
        return [];
      }

      final QuerySnapshot snapshot = await _firestore
          .collection('posts')
          .where('authorId', isEqualTo: targetUserId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        final DateTime createdAt = (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now();
        return {
          'id': doc.id,
          'content': data['content'] ?? '',
          'hasImage': data['imageUrl'] != null,
          'imageUrl': data['imageUrl'],
          'likes': (data['likesCount'] as num?)?.toInt() ?? 0,
          'comments': (data['commentsCount'] as num?)?.toInt() ?? 0,
          'authorId': data['authorId'] ?? '',
          'authorName': data['authorName'] ?? 'Unknown',
          'authorImage': data['authorImage'] ?? '',
          'createdAt': createdAt,
          'time': _formatTimestamp(createdAt),
          'type': data['type'] ?? 'regular',
        };
      }).toList();
    } catch (e) {
      debugPrint('Error getting user posts: $e');
      return [];
    }
  }

  // Get user hire plans
  Future<List<Map<String, dynamic>>> getUserHirePlans({String? userId}) async {
    try {
      final String targetUserId = userId ?? currentUserId ?? '';
      debugPrint('getUserHirePlans - Target User ID: $targetUserId');

      if (targetUserId.isEmpty) {
        debugPrint('getUserHirePlans - Empty target user ID, returning empty list');
        return [];
      }

      debugPrint('getUserHirePlans - Querying Firestore for hire plans');
      final QuerySnapshot snapshot = await _firestore
          .collection('hire_plans')
          .where('userId', isEqualTo: targetUserId)
          .orderBy('price', descending: false)
          .get();

      debugPrint('getUserHirePlans - Found ${snapshot.docs.length} hire plans');

      // Log each document for debugging
      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        debugPrint('getUserHirePlans - Plan: ${doc.id}, Name: ${data['name']}, UserId: ${data['userId']}');
      }

      final result = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;

        // Extract features if available
        List<Map<String, dynamic>> features = [];
        if (data['features'] != null && data['features'] is List) {
          features = (data['features'] as List).map((feature) {
            if (feature is Map) {
              return {
                'id': feature['id'] ?? '',
                'name': feature['name'] ?? '',
                'value': feature['value'] ?? '',
                'type': feature['type'] ?? 'boolean',
                'isCustom': feature['isCustom'] ?? false,
                'selected': feature['selected'] ?? true, // Default to true if not specified
              };
            }
            return <String, dynamic>{};
          }).cast<Map<String, dynamic>>().toList();
        }

        return {
          'id': doc.id,
          'name': data['name'] ?? 'Untitled',
          'price': (data['price'] as num?)?.toDouble() ?? 0.0,
          'duration': data['duration'] ?? '',
          'description': data['description'] ?? '',
          'active': data['active'] ?? false,
          'userId': data['userId'] ?? '',
          'features': features,
        };
      }).toList();

      debugPrint('getUserHirePlans - Returning ${result.length} hire plans');
      return result;
    } catch (e) {
      debugPrint('Error getting user hire plans: $e');
      return [];
    }
  }

  // Get user followers
  Future<List<Map<String, dynamic>>> getUserFollowers({String? userId}) async {
    try {
      final String targetUserId = userId ?? currentUserId ?? '';
      if (targetUserId.isEmpty) {
        return [];
      }

      final QuerySnapshot snapshot = await _firestore
          .collection('followers')
          .where('followedId', isEqualTo: targetUserId)
          .get();

      final List<Map<String, dynamic>> followers = [];
      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final followerId = data['followerId'] as String?;

        if (followerId != null) {
          final userDoc = await _firestore.collection('users').doc(followerId).get();
          if (userDoc.exists) {
            final userData = userDoc.data() as Map<String, dynamic>;
            followers.add({
              'id': followerId,
              'name': userData['displayName'] ?? 'Unknown',
              'username': userData['username'] ?? 'user',
              'avatar': userData['photoURL'] ?? 'https://ui-avatars.com/api/?name=User&background=4F46E5&color=fff',
              'isFollowing': await isFollowing(followerId),
            });
          }
        }
      }

      return followers;
    } catch (e) {
      debugPrint('Error getting user followers: $e');
      return [];
    }
  }

  // Get user following
  Future<List<Map<String, dynamic>>> getUserFollowing({String? userId}) async {
    try {
      final String targetUserId = userId ?? currentUserId ?? '';
      if (targetUserId.isEmpty) {
        return [];
      }

      final QuerySnapshot snapshot = await _firestore
          .collection('followers')
          .where('followerId', isEqualTo: targetUserId)
          .get();

      final List<Map<String, dynamic>> following = [];
      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final followedId = data['followedId'] as String?;

        if (followedId != null) {
          final userDoc = await _firestore.collection('users').doc(followedId).get();
          if (userDoc.exists) {
            final userData = userDoc.data() as Map<String, dynamic>;
            following.add({
              'id': followedId,
              'name': userData['displayName'] ?? 'Unknown',
              'username': userData['username'] ?? 'user',
              'avatar': userData['photoURL'] ?? 'https://ui-avatars.com/api/?name=User&background=4F46E5&color=fff',
              'isFollowing': true, // Already following
            });
          }
        }
      }

      return following;
    } catch (e) {
      debugPrint('Error getting user following: $e');
      return [];
    }
  }

  // Check if current user is following another user
  Future<bool> isFollowing(String userId) async {
    try {
      if (currentUserId == null) return false;

      final QuerySnapshot snapshot = await _firestore
          .collection('followers')
          .where('followerId', isEqualTo: currentUserId)
          .where('followedId', isEqualTo: userId)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking if following: $e');
      return false;
    }
  }

  // Follow a user
  Future<bool> followUser(String userId) async {
    try {
      if (currentUserId == null) return false;
      if (currentUserId == userId) return false; // Can't follow yourself

      debugPrint('Following user: $userId');

      // First check if already following to avoid unnecessary operations
      final bool alreadyFollowing = await isFollowing(userId);
      if (alreadyFollowing) {
        debugPrint('Already following user: $userId');
        return true;
      }

      // Create follower document directly (without transaction for better compatibility with security rules)
      await _firestore.collection('followers').add({
        'followerId': currentUserId,
        'followedId': userId,
        'createdAt': FieldValue.serverTimestamp(),
      });

      debugPrint('Created follower document');

      // Update current user's following count
      await _firestore.collection('users').doc(currentUserId).update({
        'followingCount': FieldValue.increment(1),
      });

      debugPrint('Updated current user following count');

      // Update target user's followers count
      await _firestore.collection('users').doc(userId).update({
        'followersCount': FieldValue.increment(1),
      });

      debugPrint('Updated target user followers count');
      debugPrint('Successfully followed user: $userId');
      return true;
    } catch (e) {
      debugPrint('Error following user: $e');
      return false;
    }
  }

  // Unfollow a user
  Future<bool> unfollowUser(String userId) async {
    try {
      if (currentUserId == null) return false;

      debugPrint('Unfollowing user: $userId');

      // First check if already following
      final bool isCurrentlyFollowing = await isFollowing(userId);
      if (!isCurrentlyFollowing) {
        debugPrint('Not following user: $userId');
        return true; // Already not following
      }

      // Find the follower document
      final QuerySnapshot followCheck = await _firestore
          .collection('followers')
          .where('followerId', isEqualTo: currentUserId)
          .where('followedId', isEqualTo: userId)
          .limit(1)
          .get();

      if (followCheck.docs.isEmpty) {
        debugPrint('Follower document not found');
        return true; // No document to delete
      }

      // Delete the follower document
      await followCheck.docs.first.reference.delete();
      debugPrint('Deleted follower document');

      // Update current user's following count
      await _firestore.collection('users').doc(currentUserId).update({
        'followingCount': FieldValue.increment(-1),
      });
      debugPrint('Updated current user following count');

      // Update target user's followers count
      await _firestore.collection('users').doc(userId).update({
        'followersCount': FieldValue.increment(-1),
      });
      debugPrint('Updated target user followers count');

      debugPrint('Successfully unfollowed user: $userId');
      return true;
    } catch (e) {
      debugPrint('Error unfollowing user: $e');
      return false;
    }
  }

  // Update hire plan active status
  Future<bool> updateHirePlanStatus(String planId, bool active) async {
    try {
      if (currentUserId == null) return false;

      // Simply update the plan's active status without affecting other plans
      await _firestore.collection('hire_plans').doc(planId).update({'active': active});

      debugPrint('Updated hire plan $planId active status to $active');
      return true;
    } catch (e) {
      debugPrint('Error updating hire plan status: $e');
      return false;
    }
  }

  // Add a new hire plan
  Future<bool> addHirePlan(Map<String, dynamic> planData) async {
    debugPrint('addHirePlan - Starting to add hire plan: ${planData['name']}');
    try {
      if (currentUserId == null) {
        debugPrint('addHirePlan - No current user ID, returning false');
        return false;
      }

      debugPrint('addHirePlan - Current user ID: $currentUserId');

      // Add user ID to plan data
      planData['userId'] = currentUserId;
      debugPrint('addHirePlan - Added userId to plan data: ${planData['userId']}');

      // Add creation timestamp
      planData['createdAt'] = FieldValue.serverTimestamp();
      debugPrint('addHirePlan - Added createdAt timestamp');

      // Set active status to false by default if not specified
      if (!planData.containsKey('active')) {
        planData['active'] = false;
        debugPrint('addHirePlan - Setting default active status to false');
      }

      // Add the plan to Firestore
      debugPrint('addHirePlan - Adding plan to Firestore');
      final docRef = await _firestore.collection('hire_plans').add(planData);
      debugPrint('addHirePlan - Plan added with ID: ${docRef.id}');

      // Verify the plan was added by retrieving it
      final addedPlan = await docRef.get();
      if (addedPlan.exists) {
        debugPrint('addHirePlan - Verified plan was added successfully');
      } else {
        debugPrint('addHirePlan - WARNING: Plan document does not exist after adding');
      }

      return true;
    } catch (e) {
      debugPrint('Error adding hire plan: $e');
      return false;
    }
  }

  // Update an existing hire plan
  Future<bool> updateHirePlan(String planId, Map<String, dynamic> planData) async {
    try {
      if (currentUserId == null) return false;

      // Add update timestamp
      planData['updatedAt'] = FieldValue.serverTimestamp();

      // Remove fields that shouldn't be updated
      planData.remove('userId');
      planData.remove('createdAt');
      planData.remove('id');

      // Ensure features are properly formatted for Firestore
      if (planData.containsKey('features') && planData['features'] is List) {
        final List<Map<String, dynamic>> features = List<Map<String, dynamic>>.from(planData['features']);

        // Convert features to a format that Firestore can store
        planData['features'] = features.map((feature) => {
          'id': feature['id'],
          'name': feature['name'],
          'value': feature['value'],
          'type': feature['type'],
          'isCustom': feature['isCustom'] ?? false,
          'selected': feature['selected'] ?? true, // Default to true if not specified
        }).toList();

        debugPrint('Updating hire plan with ${features.length} features');
      } else {
        debugPrint('No features found in plan data or invalid format');
      }

      // Update the plan in Firestore
      await _firestore.collection('hire_plans').doc(planId).update(planData);
      debugPrint('Hire plan updated successfully: $planId');

      return true;
    } catch (e) {
      debugPrint('Error updating hire plan: $e');
      return false;
    }
  }

  // Delete a hire plan
  Future<bool> deleteHirePlan(String planId) async {
    try {
      if (currentUserId == null) return false;

      // Check if the plan exists and belongs to the current user
      final DocumentSnapshot planDoc = await _firestore.collection('hire_plans').doc(planId).get();

      if (!planDoc.exists) return true; // Already deleted

      final planData = planDoc.data() as Map<String, dynamic>;
      if (planData['userId'] != currentUserId) return false; // Not authorized

      // Delete the plan (now allowing deletion of active plans)
      await _firestore.collection('hire_plans').doc(planId).delete();
      debugPrint('Deleted hire plan $planId');

      return true;
    } catch (e) {
      debugPrint('Error deleting hire plan: $e');
      return false;
    }
  }

  // SEARCH METHODS

  // Search for users
  Future<List<Map<String, dynamic>>> searchUsers(String query) async {
    try {
      if (query.isEmpty) return [];

      final lowerQuery = query.toLowerCase().trim();
      debugPrint('Searching for users with query: $lowerQuery');

      // Check if the fields we need to query exist in the database
      bool canUseIndexedSearch = true;
      try {
        // Try to get a single document to check if the fields exist
        final testDoc = await _firestore.collection('users').limit(1).get();
        if (testDoc.docs.isNotEmpty) {
          final data = testDoc.docs.first.data();
          if (!data.containsKey('displayNameLower') || !data.containsKey('usernameLower')) {
            canUseIndexedSearch = false;
            debugPrint('Warning: displayNameLower or usernameLower fields not found, using fallback search');
          }
        }
      } catch (e) {
        canUseIndexedSearch = false;
        debugPrint('Error checking indexed fields: $e');
      }

      // Process users and remove duplicates
      final Map<String, Map<String, dynamic>> uniqueUsers = {};

      if (canUseIndexedSearch) {
        // Use Firebase query to filter users directly instead of fetching all users
        // This is much more efficient and prevents the "app not responding" issue
        try {
          final QuerySnapshot nameSnapshot = await _firestore
              .collection('users')
              .where('displayNameLower', isGreaterThanOrEqualTo: lowerQuery)
              .where('displayNameLower', isLessThanOrEqualTo: '$lowerQuery\uf8ff')
              .limit(20)
              .get();

          final QuerySnapshot usernameSnapshot = await _firestore
              .collection('users')
              .where('usernameLower', isGreaterThanOrEqualTo: lowerQuery)
              .where('usernameLower', isLessThanOrEqualTo: '$lowerQuery\uf8ff')
              .limit(20)
              .get();

          debugPrint('Found ${nameSnapshot.docs.length} name matches and ${usernameSnapshot.docs.length} username matches');

          // Process both query results
          for (final snapshot in [nameSnapshot, usernameSnapshot]) {
            for (final doc in snapshot.docs) {
              final data = doc.data() as Map<String, dynamic>;

              // Skip if already added
              if (uniqueUsers.containsKey(doc.id)) continue;

              uniqueUsers[doc.id] = {
                'id': doc.id,
                'name': data['displayName'] ?? 'Unknown',
                'username': data['username'] ?? 'user',
                'avatar': data['photoURL'] ?? 'https://ui-avatars.com/api/?name=${Uri.encodeComponent((data['displayName'] ?? 'User').toString())}',
                'isVerified': data['isVerified'] ?? false,
                'isElite': data['isElite'] ?? false,
                'followers': data['followersCount'] ?? 0,
                'following': data['followingCount'] ?? 0,
                'assets': data['assetsCount'] ?? 0,
                'posts': data['postsCount'] ?? 0,
                'bio': data['bio'] ?? '',
              };
            }
          }
        } catch (e) {
          debugPrint('Error with indexed search: $e');
          canUseIndexedSearch = false;
        }
      }

      // If we don't have enough results or couldn't use indexed search, try a more flexible search
      if (uniqueUsers.length < 5 || !canUseIndexedSearch) {
        debugPrint('Using fallback search method');
        // Fallback to a more flexible search if needed
        final QuerySnapshot fallbackSnapshot = await _firestore
            .collection('users')
            .limit(100)
            .get();

        for (final doc in fallbackSnapshot.docs) {
          // Skip if already added
          if (uniqueUsers.containsKey(doc.id)) continue;

          final data = doc.data() as Map<String, dynamic>;
          final String displayName = (data['displayName'] ?? 'Unknown').toString().toLowerCase();
          final String username = (data['username'] ?? 'user').toString().toLowerCase();

          // Check if name or username contains the query
          if (displayName.contains(lowerQuery) || username.contains(lowerQuery)) {
            uniqueUsers[doc.id] = {
              'id': doc.id,
              'name': data['displayName'] ?? 'Unknown',
              'username': data['username'] ?? 'user',
              'avatar': data['photoURL'] ?? 'https://ui-avatars.com/api/?name=${Uri.encodeComponent((data['displayName'] ?? 'User').toString())}',
              'isVerified': data['isVerified'] ?? false,
              'isElite': data['isElite'] ?? false,
              'followers': data['followersCount'] ?? 0,
              'following': data['followingCount'] ?? 0,
              'assets': data['assetsCount'] ?? 0,
              'posts': data['postsCount'] ?? 0,
              'bio': data['bio'] ?? '',
            };
          }
        }
      }

      // If still no results, add some sample users for testing
      if (uniqueUsers.isEmpty && !const bool.fromEnvironment('dart.vm.product')) {
        debugPrint('No users found, adding sample users for testing');
        uniqueUsers['sample1'] = {
          'id': 'sample1',
          'name': 'John Doe',
          'username': 'johndoe',
          'avatar': 'https://ui-avatars.com/api/?name=John+Doe',
          'isVerified': true,
          'isElite': true,
          'followers': 1000,
          'following': 500,
          'assets': 20,
          'posts': 15,
          'bio': 'Sample user for testing',
        };
        uniqueUsers['sample2'] = {
          'id': 'sample2',
          'name': 'Jane Smith',
          'username': 'janesmith',
          'avatar': 'https://ui-avatars.com/api/?name=Jane+Smith',
          'isVerified': false,
          'isElite': false,
          'followers': 500,
          'following': 200,
          'assets': 10,
          'posts': 5,
          'bio': 'Another sample user',
        };
      }

      debugPrint('Found ${uniqueUsers.length} total matching users');

      // Convert to list and sort by followers count
      final users = uniqueUsers.values.toList();
      users.sort((a, b) => (b['followers'] as int).compareTo(a['followers'] as int));

      return users;
    } catch (e) {
      debugPrint('Error searching users: $e');
      return [];
    }
  }

  // Get asset by ID
  Future<Map<String, dynamic>?> getAssetById(String assetId) async {
    try {
      final docSnapshot = await _firestore.collection('assets').doc(assetId).get();

      if (docSnapshot.exists) {
        final data = docSnapshot.data() as Map<String, dynamic>;
        return {
          'id': docSnapshot.id,
          ...data,
        };
      }

      return null;
    } catch (e) {
      debugPrint('Error getting asset by ID: $e');
      return null;
    }
  }



  // Search for assets
  Future<List<Map<String, dynamic>>> searchAssets(String query) async {
    try {
      if (query.isEmpty) return [];

      // Convert query to lowercase for case-insensitive search
      final lowerQuery = query.toLowerCase();

      // Search by title
      final QuerySnapshot titleSnapshot = await _firestore
          .collection('assets')
          .where('title_lowercase', isGreaterThanOrEqualTo: lowerQuery)
          .where('title_lowercase', isLessThanOrEqualTo: '$lowerQuery\uf8ff')
          .limit(20)
          .get();

      // Search by tags
      final QuerySnapshot tagsSnapshot = await _firestore
          .collection('assets')
          .where('tags', arrayContains: lowerQuery)
          .limit(20)
          .get();

      // Combine results
      final Set<String> assetIds = {};
      final List<Map<String, dynamic>> assets = [];

      for (final snapshot in [titleSnapshot, tagsSnapshot]) {
        for (final doc in snapshot.docs) {
          final data = doc.data() as Map<String, dynamic>;
          final assetId = doc.id;

          if (!assetIds.contains(assetId)) {
            assetIds.add(assetId);
            assets.add({
              'id': assetId,
              'title': data['title'] ?? 'Untitled',
              'type': data['type'] ?? 'Unknown',
              'price': (data['price'] as num?)?.toDouble() ?? 0.0,
              'downloads': (data['downloads'] as num?)?.toInt() ?? 0,
              'rating': (data['rating'] as num?)?.toDouble() ?? 0.0,
              'image': data['thumbnailUrl'] ?? '',
              'authorId': data['authorId'] ?? '',
              'authorName': data['authorName'] ?? 'Unknown',
              'authorImage': data['authorImage'] ?? '',
              'contentType': 'asset',
            });
          }
        }
      }

      // Sort by downloads (most popular first)
      assets.sort((a, b) => (b['downloads'] as int).compareTo(a['downloads'] as int));

      return assets;
    } catch (e) {
      debugPrint('Error searching assets: $e');
      return [];
    }
  }

  // ASSET MANAGEMENT METHODS

  // Upload a new asset
  Future<Map<String, dynamic>> uploadAsset({
    required String title,
    required String description,
    required String type,
    required String category,
    required List<String> subcategories,
    required List<String> tags,
    required double price,
    required File assetFile,
    required List<File> screenshots,
    File? thumbnailFile,
    String? thumbnailUrl,
    bool requiresAdminApproval = true,
  }) async {
    try {
      if (currentUserId == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      // Get user data for author information
      final userDoc = await _firestore.collection('users').doc(currentUserId).get();
      if (!userDoc.exists) {
        return {'success': false, 'error': 'User profile not found'};
      }

      final userData = userDoc.data() as Map<String, dynamic>;
      final String authorName = userData['displayName'] ?? 'Unknown';
      final String authorImage = userData['photoURL'] ?? '';

      // Upload asset file to Firebase Storage
      final String assetFileName = path.basename(assetFile.path);
      final String assetStoragePath = 'assets/$currentUserId/${DateTime.now().millisecondsSinceEpoch}_$assetFileName';
      final Reference assetRef = _storage.ref().child(assetStoragePath);

      final UploadTask uploadTask = assetRef.putFile(assetFile);
      final TaskSnapshot taskSnapshot = await uploadTask;
      final String assetDownloadUrl = await taskSnapshot.ref.getDownloadURL();

      // Upload screenshots to Firebase Storage
      final List<String> screenshotUrls = [];
      for (int i = 0; i < screenshots.length; i++) {
        final File screenshot = screenshots[i];
        final String screenshotFileName = path.basename(screenshot.path);
        final String screenshotStoragePath = 'assets/$currentUserId/screenshots/${DateTime.now().millisecondsSinceEpoch}_${i}_$screenshotFileName';
        final Reference screenshotRef = _storage.ref().child(screenshotStoragePath);

        final UploadTask screenshotUploadTask = screenshotRef.putFile(screenshot);
        final TaskSnapshot screenshotTaskSnapshot = await screenshotUploadTask;
        final String screenshotUrl = await screenshotTaskSnapshot.ref.getDownloadURL();

        screenshotUrls.add(screenshotUrl);
      }

      // Upload thumbnail if provided
      String finalThumbnailUrl = '';
      if (thumbnailFile != null) {
        final String thumbnailFileName = path.basename(thumbnailFile.path);
        final String thumbnailStoragePath = 'assets/$currentUserId/thumbnails/${DateTime.now().millisecondsSinceEpoch}_$thumbnailFileName';
        final Reference thumbnailRef = _storage.ref().child(thumbnailStoragePath);

        final UploadTask thumbnailUploadTask = thumbnailRef.putFile(thumbnailFile);
        final TaskSnapshot thumbnailTaskSnapshot = await thumbnailUploadTask;
        finalThumbnailUrl = await thumbnailTaskSnapshot.ref.getDownloadURL();
      } else if (thumbnailUrl != null) {
        // Use provided URL if file not provided
        finalThumbnailUrl = thumbnailUrl;
      } else if (screenshotUrls.isNotEmpty) {
        // Fallback to first screenshot if neither thumbnail file nor URL provided
        finalThumbnailUrl = screenshotUrls[0];
      }

      // Create asset document in Firestore
      final assetData = {
        'title': title,
        'description': description,
        'type': type,
        'category': category,
        'subcategories': subcategories,
        'tags': tags,
        'price': price,
        'fileUrl': assetDownloadUrl,
        'screenshotUrls': screenshotUrls,
        'thumbnailUrl': finalThumbnailUrl,
        'authorId': currentUserId,
        'authorName': authorName,
        'authorImage': authorImage,
        'downloads': 0,
        'rating': 0.0,
        'reviewCount': 0,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'status': requiresAdminApproval ? 'pending' : 'approved', // Set status based on approval requirement
        'requiresAdminApproval': requiresAdminApproval, // Flag for admin panel
        'adminReviewed': false, // Flag to track if admin has reviewed this asset
      };

      final DocumentReference assetDocRef = await _firestore.collection('assets').add(assetData);

      // Update user's asset count
      await _firestore.collection('users').doc(currentUserId).update({
        'assetsCount': FieldValue.increment(1),
      });

      return {
        'success': true,
        'assetId': assetDocRef.id,
        'assetData': {
          ...assetData,
          'id': assetDocRef.id,
        }
      };
    } catch (e) {
      debugPrint('Error uploading asset: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Get asset details
  Future<Map<String, dynamic>?> getAssetDetails(String assetId) async {
    try {
      final DocumentSnapshot assetDoc = await _firestore.collection('assets').doc(assetId).get();

      if (!assetDoc.exists) return null;

      final data = assetDoc.data() as Map<String, dynamic>;
      return {
        'id': assetDoc.id,
        ...data,
      };
    } catch (e) {
      debugPrint('Error getting asset details: $e');
      return null;
    }
  }

  // Update an existing asset
  Future<bool> updateAsset(String assetId, Map<String, dynamic> assetData) async {
    try {
      if (currentUserId == null) return false;

      // Check if the asset exists and belongs to the current user
      final DocumentSnapshot assetDoc = await _firestore.collection('assets').doc(assetId).get();

      if (!assetDoc.exists) return false;

      final data = assetDoc.data() as Map<String, dynamic>;
      if (data['authorId'] != currentUserId) return false; // Not authorized

      // Remove fields that shouldn't be updated
      assetData.remove('authorId');
      assetData.remove('authorName');
      assetData.remove('authorImage');
      assetData.remove('createdAt');
      assetData.remove('downloads');
      assetData.remove('rating');
      assetData.remove('reviewCount');
      assetData.remove('id');

      // Add update timestamp
      assetData['updatedAt'] = FieldValue.serverTimestamp();

      // Update the asset in Firestore
      await _firestore.collection('assets').doc(assetId).update(assetData);

      return true;
    } catch (e) {
      debugPrint('Error updating asset: $e');
      return false;
    }
  }

  // Delete an asset
  Future<bool> deleteAsset(String assetId) async {
    try {
      if (currentUserId == null) return false;

      // Check if the asset exists and belongs to the current user
      final DocumentSnapshot assetDoc = await _firestore.collection('assets').doc(assetId).get();

      if (!assetDoc.exists) return true; // Already deleted

      final data = assetDoc.data() as Map<String, dynamic>;
      if (data['authorId'] != currentUserId) return false; // Not authorized

      // Delete asset file from Firebase Storage
      if (data['fileUrl'] != null) {
        try {
          final Reference fileRef = _storage.refFromURL(data['fileUrl'] as String);
          await fileRef.delete();
        } catch (e) {
          debugPrint('Error deleting asset file: $e');
          // Continue with deletion even if file deletion fails
        }
      }

      // Delete screenshots from Firebase Storage
      final List<String> screenshotUrls = List<String>.from(data['screenshotUrls'] ?? []);
      for (final String url in screenshotUrls) {
        try {
          final Reference screenshotRef = _storage.refFromURL(url);
          await screenshotRef.delete();
        } catch (e) {
          debugPrint('Error deleting screenshot: $e');
          // Continue with deletion even if screenshot deletion fails
        }
      }

      // Delete the asset document from Firestore
      await _firestore.collection('assets').doc(assetId).delete();

      // Update user's asset count
      await _firestore.collection('users').doc(currentUserId).update({
        'assetsCount': FieldValue.increment(-1),
      });

      return true;
    } catch (e) {
      debugPrint('Error deleting asset: $e');
      return false;
    }
  }

  // POST MANAGEMENT METHODS

  // Create a new post
  Future<Map<String, dynamic>> createPost({
    required String content,
    String type = 'regular',
    Map<String, dynamic>? additionalData,
    File? image,
    String? codeLanguage,
    String? code,
    List<Map<String, dynamic>>? pollOptions,
    String? questionCategory,
    String? questionTitle,
    Map<String, dynamic>? eventDetails,
  }) async {
    try {
      if (currentUserId == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      // Get user data for author information
      final userDoc = await _firestore.collection('users').doc(currentUserId).get();
      final currentUser = _auth.currentUser;

      String authorName = 'Unknown';
      String authorImage = '';

      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        authorName = userData['displayName'] ?? userData['username'] ?? 'Unknown';
        authorImage = userData['photoURL'] ?? '';
      }

      // If no image in Firestore, try to get from Firebase Auth (Google profile)
      if (authorImage.isEmpty && currentUser != null) {
        if (currentUser.photoURL != null && currentUser.photoURL!.isNotEmpty) {
          authorImage = currentUser.photoURL!;

          // Update Firestore with the Google profile image for future use
          try {
            await _firestore.collection('users').doc(currentUserId).update({
              'photoURL': authorImage,
            });
            debugPrint('createPost: Updated user photoURL in Firestore');
          } catch (e) {
            debugPrint('createPost: Error updating photoURL in Firestore: $e');
          }
        }

        // If still no image, use display name or email for fallback
        if (authorImage.isEmpty) {
          if (currentUser.displayName != null && currentUser.displayName!.isNotEmpty) {
            authorName = currentUser.displayName!;
          } else if (currentUser.email != null) {
            authorName = currentUser.email!.split('@')[0];
          }

          // Generate avatar from name/email
          if (currentUser.email != null) {
            authorImage = 'https://ui-avatars.com/api/?name=${Uri.encodeComponent(authorName)}&background=4F46E5&color=fff&size=128';
          }
        }
      }

      debugPrint('createPost: Author name: $authorName, Author image: $authorImage');

      // Upload image to Firebase Storage if provided
      String? imageUrl;
      if (image != null) {
        final String imageFileName = path.basename(image.path);
        final String imageStoragePath = 'posts/$currentUserId/${DateTime.now().millisecondsSinceEpoch}_$imageFileName';
        final Reference imageRef = _storage.ref().child(imageStoragePath);

        final UploadTask uploadTask = imageRef.putFile(image);
        final TaskSnapshot taskSnapshot = await uploadTask;
        imageUrl = await taskSnapshot.ref.getDownloadURL();
      }

      // Determine post type from parameters if not explicitly set
      String postType = type;
      if (type == 'regular') {
        if (code != null && codeLanguage != null) {
          postType = 'code';
        } else if (pollOptions != null && pollOptions.isNotEmpty) {
          postType = 'poll';
        } else if (questionTitle != null && questionCategory != null) {
          postType = 'question';
        } else if (eventDetails != null) {
          postType = 'event';
        }
      }

      // Create post document in Firestore
      final postData = {
        'content': content,
        'imageUrl': imageUrl,
        'authorId': currentUserId,
        'authorName': authorName,
        'authorImage': authorImage,
        'likesCount': 0,
        'commentsCount': 0,
        'type': postType,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // Add type-specific fields from additionalData if provided
      if (additionalData != null) {
        if (postType == 'code') {
          postData['codeLanguage'] = additionalData['codeLanguage'] ?? codeLanguage;
          postData['code'] = additionalData['code'] ?? code;
        } else if (postType == 'poll') {
          postData['pollOptions'] = additionalData['pollOptions'] ?? pollOptions;
          postData['pollEndDate'] = additionalData['pollEndDate'] ?? Timestamp.fromDate(
            DateTime.now().add(const Duration(days: 7)),
          );
        } else if (postType == 'question') {
          postData['questionCategory'] = additionalData['questionCategory'] ?? questionCategory;
          postData['questionTitle'] = additionalData['questionTitle'] ?? questionTitle;
          postData['answers'] = additionalData['answers'] ?? 0;
          postData['views'] = additionalData['views'] ?? 0;
        } else if (postType == 'event') {
          // Add event details from additionalData
          final eventData = additionalData['eventDetails'] ?? eventDetails ?? {};
          postData.addAll(eventData);
        }
      } else {
        // Use the old parameters if additionalData is not provided
        if (postType == 'code') {
          postData['codeLanguage'] = codeLanguage;
          postData['code'] = code;
        } else if (postType == 'poll') {
          postData['pollOptions'] = pollOptions;
          // Set poll end date to 7 days from now
          postData['pollEndDate'] = Timestamp.fromDate(
            DateTime.now().add(const Duration(days: 7)),
          );
        } else if (postType == 'question') {
          postData['questionCategory'] = questionCategory;
          postData['questionTitle'] = questionTitle;
          postData['answers'] = 0;
          postData['views'] = 0;
        } else if (postType == 'event' && eventDetails != null) {
          postData.addAll(eventDetails);
        }
      }

      final DocumentReference postDocRef = await _firestore.collection('posts').add(postData);

      // Add post ID to user's posts array for easy retrieval
      await _firestore.collection('users').doc(currentUserId).update({
        'userPosts': FieldValue.arrayUnion([postDocRef.id]),
      });

      return {
        'success': true,
        'postId': postDocRef.id,
        'postData': {
          ...postData,
          'id': postDocRef.id,
        }
      };
    } catch (e) {
      debugPrint('Error creating post: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Create a post with image
  Future<Map<String, dynamic>> createPostWithImage({
    required String content,
    required File imageFile,
    String type = 'regular',
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      if (currentUserId == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      // Get user data for author information
      final userDoc = await _firestore.collection('users').doc(currentUserId).get();
      final currentUser = _auth.currentUser;

      String authorName = 'Unknown';
      String authorImage = '';

      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        authorName = userData['displayName'] ?? userData['username'] ?? 'Unknown';
        authorImage = userData['photoURL'] ?? '';
      }

      // If no image in Firestore, try to get from Firebase Auth (Google profile)
      if (authorImage.isEmpty && currentUser != null) {
        if (currentUser.photoURL != null && currentUser.photoURL!.isNotEmpty) {
          authorImage = currentUser.photoURL!;

          // Update Firestore with the Google profile image for future use
          try {
            await _firestore.collection('users').doc(currentUserId).update({
              'photoURL': authorImage,
            });
            debugPrint('createPostWithImage: Updated user photoURL in Firestore');
          } catch (e) {
            debugPrint('createPostWithImage: Error updating photoURL in Firestore: $e');
          }
        }

        // If still no image, use display name or email for fallback
        if (authorImage.isEmpty) {
          if (currentUser.displayName != null && currentUser.displayName!.isNotEmpty) {
            authorName = currentUser.displayName!;
          } else if (currentUser.email != null) {
            authorName = currentUser.email!.split('@')[0];
          }

          // Generate avatar from name/email
          if (currentUser.email != null) {
            authorImage = 'https://ui-avatars.com/api/?name=${Uri.encodeComponent(authorName)}&background=4F46E5&color=fff&size=128';
          }
        }
      }

      debugPrint('createPostWithImage: Author name: $authorName, Author image: $authorImage');

      // Upload image to Firebase Storage
      String? imageUrl;
      final String imageFileName = path.basename(imageFile.path);
      final String imageStoragePath = 'posts/$currentUserId/${DateTime.now().millisecondsSinceEpoch}_$imageFileName';
      final Reference imageRef = _storage.ref().child(imageStoragePath);

      final UploadTask uploadTask = imageRef.putFile(imageFile);
      final TaskSnapshot taskSnapshot = await uploadTask;
      imageUrl = await taskSnapshot.ref.getDownloadURL();

      // Create post document in Firestore
      final postData = {
        'content': content,
        'imageUrl': imageUrl,
        'authorId': currentUserId,
        'authorName': authorName,
        'authorImage': authorImage,
        'likesCount': 0,
        'commentsCount': 0,
        'type': type,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // Add additional data if provided
      if (additionalData != null) {
        if (type == 'code') {
          postData['codeLanguage'] = additionalData['codeLanguage'];
          postData['code'] = additionalData['code'];
        } else if (type == 'poll') {
          postData['pollOptions'] = additionalData['pollOptions'];
          postData['pollEndDate'] = additionalData['pollEndDate'] ?? Timestamp.fromDate(
            DateTime.now().add(const Duration(days: 7)),
          );
        } else if (type == 'question') {
          postData['questionCategory'] = additionalData['questionCategory'];
          postData['questionTitle'] = additionalData['questionTitle'];
          postData['answers'] = additionalData['answers'] ?? 0;
          postData['views'] = additionalData['views'] ?? 0;
        } else if (type == 'event') {
          // Add event details
          final eventData = additionalData['eventDetails'] ?? {};
          postData.addAll(eventData);
        }
      }

      final DocumentReference postDocRef = await _firestore.collection('posts').add(postData);

      // Add post ID to user's posts array for easy retrieval
      await _firestore.collection('users').doc(currentUserId).update({
        'userPosts': FieldValue.arrayUnion([postDocRef.id]),
      });

      return {
        'success': true,
        'postId': postDocRef.id,
        'postData': {
          ...postData,
          'id': postDocRef.id,
        }
      };
    } catch (e) {
      debugPrint('Error creating post with image: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Update an existing post
  Future<bool> updatePost(String postId, Map<String, dynamic> postData) async {
    try {
      if (currentUserId == null) return false;

      // Check if the post exists and belongs to the current user
      final DocumentSnapshot postDoc = await _firestore.collection('posts').doc(postId).get();

      if (!postDoc.exists) return false;

      final data = postDoc.data() as Map<String, dynamic>;
      if (data['authorId'] != currentUserId) return false; // Not authorized

      // Remove fields that shouldn't be updated
      postData.remove('authorId');
      postData.remove('authorName');
      postData.remove('authorImage');
      postData.remove('createdAt');
      postData.remove('likesCount');
      postData.remove('commentsCount');
      postData.remove('id');

      // Add update timestamp
      postData['updatedAt'] = FieldValue.serverTimestamp();

      // Update the post in Firestore
      await _firestore.collection('posts').doc(postId).update(postData);

      return true;
    } catch (e) {
      debugPrint('Error updating post: $e');
      return false;
    }
  }

  // Delete a post
  Future<bool> deletePost(String postId) async {
    try {
      if (currentUserId == null) return false;

      // Check if the post exists and belongs to the current user
      final DocumentSnapshot postDoc = await _firestore.collection('posts').doc(postId).get();

      if (!postDoc.exists) return true; // Already deleted

      final data = postDoc.data() as Map<String, dynamic>;
      if (data['authorId'] != currentUserId) return false; // Not authorized

      // Delete post image from Firebase Storage if exists
      if (data['imageUrl'] != null) {
        try {
          final Reference imageRef = _storage.refFromURL(data['imageUrl'] as String);
          await imageRef.delete();
        } catch (e) {
          debugPrint('Error deleting post image: $e');
          // Continue with deletion even if image deletion fails
        }
      }

      // Delete the post document from Firestore
      await _firestore.collection('posts').doc(postId).delete();

      // Delete all comments for this post
      final QuerySnapshot commentsSnapshot = await _firestore
          .collection('comments')
          .where('postId', isEqualTo: postId)
          .get();

      final WriteBatch batch = _firestore.batch();
      for (final doc in commentsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Delete all likes for this post
      final QuerySnapshot likesSnapshot = await _firestore
          .collection('likes')
          .where('postId', isEqualTo: postId)
          .get();

      for (final doc in likesSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Commit the batch
      await batch.commit();

      return true;
    } catch (e) {
      debugPrint('Error deleting post: $e');
      return false;
    }
  }

  // Toggle like for a post (ultra-fast with performance service)
  Future<bool> togglePostLike(String postId) async {
    return await _performanceService.togglePostLike(postId);
  }

  // Like a post (legacy method - now uses toggle)
  Future<bool> likePost(String postId) async {
    try {
      if (currentUserId == null) return false;

      // Check if already liked using optimized method
      final bool isLiked = await isPostLiked(postId);
      if (isLiked) return true; // Already liked

      // Use toggle method
      return await togglePostLike(postId);
    } catch (e) {
      debugPrint('Error liking post: $e');
      return false;
    }
  }

  // Unlike a post (legacy method - now uses toggle)
  Future<bool> unlikePost(String postId) async {
    try {
      if (currentUserId == null) return false;

      // Check if already liked using optimized method
      final bool isLiked = await isPostLiked(postId);
      if (!isLiked) return true; // Already not liked

      // Use toggle method
      await togglePostLike(postId);
      return true;
    } catch (e) {
      debugPrint('Error unliking post: $e');
      return false;
    }
  }

  // Check if user has liked a post (ultra-fast with performance service)
  Future<bool> isPostLiked(String postId) async {
    return await _performanceService.isPostLiked(postId);
  }

  // Save a post
  Future<bool> savePost(String postId) async {
    try {
      if (currentUserId == null) {
        debugPrint('savePost: User not authenticated');
        return false;
      }

      debugPrint('savePost: Saving post $postId for user $currentUserId');

      // Check if already saved
      final QuerySnapshot saveSnapshot = await _firestore
          .collection('saved_posts')
          .where('postId', isEqualTo: postId)
          .where('userId', isEqualTo: currentUserId)
          .limit(1)
          .get();

      if (saveSnapshot.docs.isNotEmpty) {
        debugPrint('savePost: Post $postId already saved');
        return true; // Already saved
      }

      // Create saved post document
      final docRef = await _firestore.collection('saved_posts').add({
        'postId': postId,
        'userId': currentUserId,
        'createdAt': FieldValue.serverTimestamp(),
      });

      debugPrint('savePost: Post $postId saved successfully with doc ID: ${docRef.id}');

      // Verify the save by checking the count
      final countAfter = await getSavedPostsCount();
      debugPrint('savePost: Total saved posts count after save: $countAfter');

      return true;
    } catch (e) {
      debugPrint('Error saving post: $e');
      return false;
    }
  }

  // Get saved posts count
  Future<int> getSavedPostsCount() async {
    try {
      if (currentUserId == null) return 0;

      final QuerySnapshot snapshot = await _firestore
          .collection('saved_posts')
          .where('userId', isEqualTo: currentUserId)
          .get();

      debugPrint('getSavedPostsCount: Found ${snapshot.docs.length} saved posts for user $currentUserId');
      return snapshot.docs.length;
    } catch (e) {
      debugPrint('Error getting saved posts count: $e');
      return 0;
    }
  }

  // Unsave a post
  Future<bool> unsavePost(String postId) async {
    try {
      if (currentUserId == null) return false;

      // Find the saved post document
      final QuerySnapshot saveSnapshot = await _firestore
          .collection('saved_posts')
          .where('postId', isEqualTo: postId)
          .where('userId', isEqualTo: currentUserId)
          .limit(1)
          .get();

      if (saveSnapshot.docs.isEmpty) return true; // Already not saved

      // Delete the saved post document
      await saveSnapshot.docs.first.reference.delete();

      return true;
    } catch (e) {
      debugPrint('Error unsaving post: $e');
      return false;
    }
  }

  // Check if user has saved a post
  Future<bool> isPostSaved(String postId) async {
    try {
      if (currentUserId == null) return false;

      final QuerySnapshot saveSnapshot = await _firestore
          .collection('saved_posts')
          .where('postId', isEqualTo: postId)
          .where('userId', isEqualTo: currentUserId)
          .limit(1)
          .get();

      return saveSnapshot.docs.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking if post is saved: $e');
      return false;
    }
  }

  // Get saved posts
  Future<List<Map<String, dynamic>>> getSavedPosts() async {
    try {
      if (currentUserId == null) {
        debugPrint('getSavedPosts: User not authenticated');
        return [];
      }

      debugPrint('getSavedPosts: Getting saved posts for user: $currentUserId');

      // Get saved post IDs (without orderBy to avoid index requirement)
      final QuerySnapshot savedPostsSnapshot = await _firestore
          .collection('saved_posts')
          .where('userId', isEqualTo: currentUserId)
          .get();

      debugPrint('getSavedPosts: Found ${savedPostsSnapshot.docs.length} saved post references');

      if (savedPostsSnapshot.docs.isEmpty) {
        debugPrint('getSavedPosts: No saved posts found for user: $currentUserId');
        return [];
      }

      // Debug: Print all saved post documents
      for (int i = 0; i < savedPostsSnapshot.docs.length; i++) {
        final doc = savedPostsSnapshot.docs[i];
        final data = doc.data() as Map<String, dynamic>;
        debugPrint('getSavedPosts: Saved post doc $i:');
        debugPrint('  - Document ID: ${doc.id}');
        debugPrint('  - userId: ${data['userId']}');
        debugPrint('  - postId: ${data['postId']}');
        debugPrint('  - createdAt: ${data['createdAt']}');
        debugPrint('  - All fields: ${data.keys.join(', ')}');
      }

      // Extract post IDs with timestamps for sorting
      final List<Map<String, dynamic>> savedPostsData = [];
      final Set<String> uniquePostIds = <String>{}; // Use Set to avoid duplicates
      final List<String> savedPostIds = [];

      for (final doc in savedPostsSnapshot.docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          final postId = data['postId'];
          final createdAt = data['createdAt'];

          debugPrint('getSavedPosts: Processing saved post doc ${doc.id}');
          debugPrint('  - Raw postId: $postId (type: ${postId.runtimeType})');
          debugPrint('  - Raw createdAt: $createdAt (type: ${createdAt.runtimeType})');

          if (postId != null && postId.toString().isNotEmpty) {
            final postIdString = postId.toString();

            // Check for duplicates
            if (!uniquePostIds.contains(postIdString)) {
              uniquePostIds.add(postIdString);
              savedPostIds.add(postIdString);
              savedPostsData.add({
                'postId': postIdString,
                'createdAt': createdAt,
              });
              debugPrint('  - Added unique postId: $postIdString');
            } else {
              debugPrint('  - Skipped duplicate postId: $postIdString');
            }
          } else {
            debugPrint('  - Skipped: postId is null or empty');
          }
        } catch (e) {
          debugPrint('getSavedPosts: Error processing saved post doc ${doc.id}: $e');
        }
      }

      debugPrint('getSavedPosts: Extracted ${savedPostIds.length} unique post IDs: $savedPostIds');

      if (savedPostIds.isEmpty) {
        debugPrint('getSavedPosts: No valid post IDs found after processing');
        return [];
      }

      // Sort by createdAt timestamp (newest first)
      savedPostsData.sort((a, b) {
        final aTime = a['createdAt'];
        final bTime = b['createdAt'];

        // Handle different timestamp types
        DateTime? aDateTime;
        DateTime? bDateTime;

        if (aTime is Timestamp) {
          aDateTime = aTime.toDate();
        } else if (aTime is DateTime) {
          aDateTime = aTime;
        }

        if (bTime is Timestamp) {
          bDateTime = bTime.toDate();
        } else if (bTime is DateTime) {
          bDateTime = bTime;
        }

        if (aDateTime == null && bDateTime == null) return 0;
        if (aDateTime == null) return 1;
        if (bDateTime == null) return -1;
        return bDateTime.compareTo(aDateTime); // Descending order (newest first)
      });

      debugPrint('getSavedPosts: Sorted ${savedPostsData.length} saved posts by date');

      // Get post details one by one to avoid complex queries
      final List<Map<String, dynamic>> savedPosts = [];

      for (final postId in savedPostIds) {
        try {
          debugPrint('getSavedPosts: Fetching post: $postId');
          final DocumentSnapshot postDoc = await _firestore
              .collection('posts')
              .doc(postId)
              .get();

          debugPrint('getSavedPosts: Post $postId exists: ${postDoc.exists}');

          if (postDoc.exists) {
            final data = postDoc.data() as Map<String, dynamic>;
            debugPrint('getSavedPosts: Post $postId data fields: ${data.keys.join(', ')}');

            final content = data['content']?.toString() ?? '';
            final contentPreview = content.length > 50 ? '${content.substring(0, 50)}...' : content;
            debugPrint('getSavedPosts: Post $postId content preview: "$contentPreview"');
            debugPrint('getSavedPosts: Post $postId content length: ${content.length}');

            // Get author information
            String authorName = data['authorName'] ?? 'Unknown';
            String authorImage = data['authorImage'] ?? '';
            String authorId = data['authorId'] ?? data['userId'] ?? '';

            debugPrint('getSavedPosts: Post $postId author info - Name: $authorName, Image: $authorImage, ID: $authorId');

            // If author info is not in post data, fetch from users collection
            if ((authorName == 'Unknown' || authorImage.isEmpty) && authorId.isNotEmpty) {
              try {
                debugPrint('getSavedPosts: Fetching author info for authorId: $authorId');
                final userDoc = await _firestore.collection('users').doc(authorId).get();
                if (userDoc.exists) {
                  final userData = userDoc.data() as Map<String, dynamic>;
                  authorName = userData['displayName'] ?? userData['username'] ?? 'Unknown';
                  authorImage = userData['photoURL'] ?? '';
                  debugPrint('getSavedPosts: Found author: $authorName, image: $authorImage');
                } else {
                  debugPrint('getSavedPosts: Author document not found for ID: $authorId');
                }
              } catch (e) {
                debugPrint('getSavedPosts: Error fetching author data: $e');
              }
            }

            final postContent = data['content']?.toString() ?? '';
            final postData = {
              'id': postDoc.id,
              'content': postContent,
              'hasImage': data['imageUrl'] != null,
              'imageUrl': data['imageUrl'],
              'likes': (data['likesCount'] as num?)?.toInt() ?? 0,
              'comments': (data['commentsCount'] as num?)?.toInt() ?? 0,
              'authorId': authorId,
              'author': authorName,
              'authorImage': authorImage,
              'time': _formatTimestamp(data['createdAt'] as Timestamp?),
              'createdAt': (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
              'type': data['type'] ?? 'regular',
            };

            savedPosts.add(postData);
            debugPrint('getSavedPosts: Successfully added post: ${postDoc.id} with author: $authorName');
            debugPrint('getSavedPosts: Post content in final data: "$postContent"');
          } else {
            debugPrint('getSavedPosts: Post document not found in posts collection: $postId');
          }
        } catch (e) {
          debugPrint('getSavedPosts: Error fetching post $postId: $e');
          debugPrint('getSavedPosts: Error stack trace: ${e.toString()}');
        }
      }

      // Sort by creation date (newest first)
      savedPosts.sort((a, b) {
        final aTime = a['createdAt'] as DateTime;
        final bTime = b['createdAt'] as DateTime;
        return bTime.compareTo(aTime);
      });

      debugPrint('getSavedPosts: Returning ${savedPosts.length} saved posts');
      return savedPosts;
    } catch (e) {
      debugPrint('Error getting saved posts: $e');
      return [];
    }
  }

  // Add a comment to a post
  Future<Map<String, dynamic>> addComment(String postId, String content) async {
    try {
      debugPrint('addComment: Starting to add comment to post $postId');
      debugPrint('addComment: Comment content: "$content"');

      if (currentUserId == null) {
        debugPrint('addComment: User not authenticated');
        return {'success': false, 'error': 'User not authenticated'};
      }

      debugPrint('addComment: Current user ID: $currentUserId');

      // Get user data for author information
      final userDoc = await _firestore.collection('users').doc(currentUserId).get();
      final currentUser = _auth.currentUser;

      String authorName = 'Unknown';
      String authorImage = '';

      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        authorName = userData['displayName'] ?? userData['username'] ?? 'Unknown';
        authorImage = userData['photoURL'] ?? '';
      }

      // If no image in Firestore, try to get from Firebase Auth (Google profile)
      if (authorImage.isEmpty && currentUser != null) {
        if (currentUser.photoURL != null && currentUser.photoURL!.isNotEmpty) {
          authorImage = currentUser.photoURL!;

          // Update Firestore with the Google profile image for future use
          try {
            await _firestore.collection('users').doc(currentUserId).update({
              'photoURL': authorImage,
            });
            debugPrint('addComment: Updated user photoURL in Firestore');
          } catch (e) {
            debugPrint('addComment: Error updating photoURL in Firestore: $e');
          }
        }

        // If still no image, use display name or email for fallback
        if (authorImage.isEmpty) {
          if (currentUser.displayName != null && currentUser.displayName!.isNotEmpty) {
            authorName = currentUser.displayName!;
          } else if (currentUser.email != null) {
            authorName = currentUser.email!.split('@')[0];
          }

          // Generate avatar from name/email
          if (currentUser.email != null) {
            authorImage = 'https://ui-avatars.com/api/?name=${Uri.encodeComponent(authorName)}&background=4F46E5&color=fff&size=128';
          }
        }
      }

      debugPrint('addComment: Author name: $authorName, Author image: $authorImage');

      // Create comment document
      final commentData = {
        'postId': postId,
        'userId': currentUserId,
        'userName': authorName,
        'userImage': authorImage,
        'content': content,
        'likesCount': 0,
        'createdAt': FieldValue.serverTimestamp(),
      };

      debugPrint('addComment: Adding comment to Firestore main collection');
      // Add comment to the main comments collection only (simplified approach)
      final DocumentReference commentDocRef = await _firestore.collection('comments').add(commentData);
      debugPrint('addComment: Comment added with ID: ${commentDocRef.id}');

      // Update post comment count
      debugPrint('addComment: Updating post comment count');
      await _firestore.collection('posts').doc(postId).update({
        'commentsCount': FieldValue.increment(1),
      });
      debugPrint('addComment: Post comment count updated');

      debugPrint('addComment: Comment added successfully');
      return {
        'success': true,
        'commentId': commentDocRef.id,
        'commentData': {
          ...commentData,
          'id': commentDocRef.id,
        }
      };
    } catch (e) {
      debugPrint('Error adding comment: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Get comments for a post (ultra-fast with performance service)
  Future<List<Map<String, dynamic>>> getPostComments(String postId) async {
    return await _performanceService.getPostComments(postId);
  }

  // Like/unlike a comment (ultra-fast with performance service)
  Future<bool> toggleCommentLike(String commentId) async {
    return await _performanceService.toggleCommentLike(commentId);
  }

  // Check if user has liked a comment (ultra-fast with performance service)
  Future<bool> hasLikedComment(String commentId) async {
    return await _performanceService.isCommentLiked(commentId);
  }

  // Add a reply to a comment
  Future<Map<String, dynamic>> addCommentReply(String commentId, String content) async {
    try {
      debugPrint('addCommentReply: Starting to add reply to comment $commentId');

      if (currentUserId == null) {
        debugPrint('addCommentReply: User not authenticated');
        return {'success': false, 'error': 'User not authenticated'};
      }

      // Get user data for author information
      final userDoc = await _firestore.collection('users').doc(currentUserId).get();
      final currentUser = _auth.currentUser;

      String authorName = 'Unknown';
      String authorImage = '';

      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        authorName = userData['displayName'] ?? userData['username'] ?? 'Unknown';
        authorImage = userData['photoURL'] ?? '';
      }

      // If no image in Firestore, try to get from Firebase Auth (Google profile)
      if (authorImage.isEmpty && currentUser != null) {
        if (currentUser.photoURL != null && currentUser.photoURL!.isNotEmpty) {
          authorImage = currentUser.photoURL!;

          // Update Firestore with the Google profile image for future use
          try {
            await _firestore.collection('users').doc(currentUserId).update({
              'photoURL': authorImage,
            });
            debugPrint('addCommentReply: Updated user photoURL in Firestore');
          } catch (e) {
            debugPrint('addCommentReply: Error updating photoURL in Firestore: $e');
          }
        }

        // If still no image, use display name or email for fallback
        if (authorImage.isEmpty) {
          if (currentUser.displayName != null && currentUser.displayName!.isNotEmpty) {
            authorName = currentUser.displayName!;
          } else if (currentUser.email != null) {
            authorName = currentUser.email!.split('@')[0];
          }

          // Generate avatar from name/email
          if (currentUser.email != null) {
            authorImage = 'https://ui-avatars.com/api/?name=${Uri.encodeComponent(authorName)}&background=4F46E5&color=fff&size=128';
          }
        }
      }

      // Create reply document
      final replyData = {
        'commentId': commentId,
        'userId': currentUserId,
        'userName': authorName,
        'userImage': authorImage,
        'content': content,
        'likesCount': 0,
        'createdAt': FieldValue.serverTimestamp(),
      };

      // Add reply to comment_replies collection
      final DocumentReference replyDocRef = await _firestore.collection('comment_replies').add(replyData);
      debugPrint('addCommentReply: Reply added with ID: ${replyDocRef.id}');

      debugPrint('addCommentReply: Reply added successfully');
      return {
        'success': true,
        'replyId': replyDocRef.id,
        'replyData': {
          ...replyData,
          'id': replyDocRef.id,
        }
      };
    } catch (e) {
      debugPrint('Error adding comment reply: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Get replies for a comment
  Future<List<Map<String, dynamic>>> getCommentReplies(String commentId) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('comment_replies')
          .where('commentId', isEqualTo: commentId)
          .orderBy('createdAt', descending: false) // Oldest first for replies
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
          'createdAt': (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        };
      }).toList();
    } catch (e) {
      debugPrint('Error getting comment replies: $e');
      return [];
    }
  }

  // Get user activity
  Future<List<Map<String, dynamic>>> getUserActivity({String? userId}) async {
    try {
      final String targetUserId = userId ?? currentUserId ?? '';
      if (targetUserId.isEmpty) return [];

      // Get user posts
      final QuerySnapshot postsSnapshot = await _firestore
          .collection('posts')
          .where('authorId', isEqualTo: targetUserId)
          .orderBy('createdAt', descending: true)
          .limit(10)
          .get();

      // Get user assets
      final QuerySnapshot assetsSnapshot = await _firestore
          .collection('assets')
          .where('authorId', isEqualTo: targetUserId)
          .orderBy('createdAt', descending: true)
          .limit(10)
          .get();

      // Get user likes
      final QuerySnapshot likesSnapshot = await _firestore
          .collection('likes')
          .where('userId', isEqualTo: targetUserId)
          .orderBy('createdAt', descending: true)
          .limit(10)
          .get();

      // Get user comments
      final QuerySnapshot commentsSnapshot = await _firestore
          .collection('comments')
          .where('userId', isEqualTo: targetUserId)
          .orderBy('createdAt', descending: true)
          .limit(10)
          .get();

      // Combine all activities
      final List<Map<String, dynamic>> activities = [];

      // Add posts
      for (final doc in postsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final timestamp = data['createdAt'] as Timestamp?;
        final DateTime createdAt = timestamp?.toDate() ?? DateTime.now();

        activities.add({
          'id': doc.id,
          'type': 'post',
          'text': 'Published a new post',
          'time': _formatTimestamp(createdAt),
          'timestamp': createdAt,
        });
      }

      // Add assets
      for (final doc in assetsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final timestamp = data['createdAt'] as Timestamp?;
        final DateTime createdAt = timestamp?.toDate() ?? DateTime.now();
        final String title = data['title'] ?? 'Untitled';

        activities.add({
          'id': doc.id,
          'type': 'upload',
          'text': 'Published $title',
          'time': _formatTimestamp(createdAt),
          'timestamp': createdAt,
        });
      }

      // Add likes
      for (final doc in likesSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final timestamp = data['createdAt'] as Timestamp?;
        final DateTime createdAt = timestamp?.toDate() ?? DateTime.now();

        activities.add({
          'id': doc.id,
          'type': 'like',
          'text': 'Liked a post',
          'time': _formatTimestamp(createdAt),
          'timestamp': createdAt,
        });
      }

      // Add comments
      for (final doc in commentsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final timestamp = data['createdAt'] as Timestamp?;
        final DateTime createdAt = timestamp?.toDate() ?? DateTime.now();

        activities.add({
          'id': doc.id,
          'type': 'comment',
          'text': 'Commented on a post',
          'time': _formatTimestamp(createdAt),
          'timestamp': createdAt,
        });
      }

      // Sort by timestamp (newest first)
      activities.sort((a, b) => b['timestamp'].compareTo(a['timestamp']));

      // Return only the first 20 activities
      return activities.take(20).toList();
    } catch (e) {
      debugPrint('Error getting user activity: $e');
      return [];
    }
  }

  // Get user reviews
  Future<List<Map<String, dynamic>>> getUserReviews({String? userId}) async {
    try {
      final String targetUserId = userId ?? currentUserId ?? '';
      if (targetUserId.isEmpty) {
        debugPrint('UserContentService - Cannot get reviews: targetUserId is empty');
        return [];
      }

      debugPrint('UserContentService - Getting reviews for user: $targetUserId (current user: $currentUserId)');
      final List<Map<String, dynamic>> reviews = [];

      // First try to get reviews from the reviews collection
      debugPrint('UserContentService - Querying reviews collection with receiverId: $targetUserId');
      try {
        // Check if the reviews collection exists and has documents
        final CollectionReference reviewsCollection = _firestore.collection('reviews');
        final QuerySnapshot testQuery = await reviewsCollection.limit(1).get();
        debugPrint('UserContentService - Reviews collection exists: ${testQuery.docs.isNotEmpty}');

        // Log the structure of a sample review document if available
        if (testQuery.docs.isNotEmpty) {
          final sampleData = testQuery.docs.first.data() as Map<String, dynamic>;
          debugPrint('UserContentService - Sample review document fields: ${sampleData.keys.join(', ')}');
        }

        // Try different queries to find reviews for this user
        debugPrint('UserContentService - Trying different queries to find reviews for user: $targetUserId');

        // First try with receiverId field
        QuerySnapshot reviewsSnapshot = await _firestore
            .collection('reviews')
            .where('receiverId', isEqualTo: targetUserId)
            .get();

        debugPrint('UserContentService - Query with receiverId found ${reviewsSnapshot.docs.length} reviews');

        // If no results, try with userId field
        if (reviewsSnapshot.docs.isEmpty) {
          debugPrint('UserContentService - No reviews found with receiverId, trying userId field');
          reviewsSnapshot = await _firestore
              .collection('reviews')
              .where('userId', isEqualTo: targetUserId)
              .get();

          debugPrint('UserContentService - Query with userId found ${reviewsSnapshot.docs.length} reviews');
        }

        // If still no results, log it but don't create test reviews
        if (reviewsSnapshot.docs.isEmpty && targetUserId == currentUserId) {
          debugPrint('UserContentService - No reviews found for current user');

          // Try the query again with both fields
          reviewsSnapshot = await _firestore
              .collection('reviews')
              .where('receiverId', isEqualTo: targetUserId)
              .get();

          debugPrint('UserContentService - After second attempt, found ${reviewsSnapshot.docs.length} reviews');
        }

        debugPrint('UserContentService - Found ${reviewsSnapshot.docs.length} reviews in reviews collection');

        // Process reviews from the reviews collection
        for (final doc in reviewsSnapshot.docs) {
          final data = doc.data() as Map<String, dynamic>;
          final timestamp = data['createdAt'] as Timestamp?;
          final DateTime createdAt = timestamp?.toDate() ?? DateTime.now();

          // Get reviewer info
          String reviewerName = data['reviewerName'] ?? 'Anonymous';
          String reviewerAvatar = data['reviewerAvatar'] ?? '';

          // If reviewer info is not in the review document, try to fetch it
          if (reviewerName == 'Anonymous' || reviewerAvatar.isEmpty) {
            try {
              final String reviewerId = data['reviewerId'] ?? '';
              if (reviewerId.isNotEmpty) {
                final reviewerDoc = await _firestore.collection('users').doc(reviewerId).get();
                if (reviewerDoc.exists) {
                  final reviewerData = reviewerDoc.data() as Map<String, dynamic>;
                  reviewerName = reviewerData['displayName'] ?? 'Anonymous';
                  reviewerAvatar = reviewerData['photoURL'] ?? '';
                  debugPrint('UserContentService - Found reviewer: $reviewerName');
                }
              }
            } catch (e) {
              debugPrint('UserContentService - Error getting reviewer info: $e');
            }
          }

          reviews.add({
            'id': doc.id,
            'reviewerId': data['reviewerId'] ?? '',
            'reviewerName': reviewerName,
            'reviewerAvatar': reviewerAvatar,
            'rating': data['rating'] ?? 5,
            'comment': data['comment'] ?? '',
            'time': _formatTimestamp(createdAt),
            'timestamp': createdAt,
            'projectType': data['projectType'] ?? 'Unknown',
            'projectName': data['projectName'] ?? 'Project',
            'hireRequestId': data['hireRequestId'] ?? '',
            'source': 'reviews_collection',
          });
        }
      } catch (e) {
        debugPrint('UserContentService - Error querying reviews collection: $e');
      }

      // Also check hire_requests collection for any reviews not in the reviews collection
      debugPrint('UserContentService - Also checking hire_requests collection');
      try {
        // Check if the hire_requests collection exists and has documents
        final CollectionReference hireRequestsCollection = _firestore.collection('hire_requests');
        final QuerySnapshot testHireQuery = await hireRequestsCollection.limit(1).get();
        debugPrint('UserContentService - hire_requests collection exists: ${testHireQuery.docs.isNotEmpty}');

        // Log the structure of a sample hire request document if available
        if (testHireQuery.docs.isNotEmpty) {
          final sampleHireData = testHireQuery.docs.first.data() as Map<String, dynamic>;
          debugPrint('UserContentService - Sample hire request document fields: ${sampleHireData.keys.join(', ')}');

          // Check if the sample document has the fields we need
          debugPrint('UserContentService - Sample hire request has developerId: ${sampleHireData.containsKey('developerId')}');
          debugPrint('UserContentService - Sample hire request has isRated: ${sampleHireData.containsKey('isRated')}');
          debugPrint('UserContentService - Sample hire request has reviewedAt: ${sampleHireData.containsKey('reviewedAt')}');
        }

        // Now query for the specific user's hire requests
        final QuerySnapshot hireRequestsSnapshot = await _firestore
            .collection('hire_requests')
            .where('developerId', isEqualTo: targetUserId)
            .where('isRated', isEqualTo: true)
            .orderBy('reviewedAt', descending: true)
            .get();

        debugPrint('UserContentService - Found ${hireRequestsSnapshot.docs.length} rated hire requests');

        // Keep track of hire request IDs that already have reviews
        final Set<String> existingHireRequestIds = reviews
            .where((review) => review['hireRequestId'] != null && review['hireRequestId'].isNotEmpty)
            .map((review) => review['hireRequestId'] as String)
            .toSet();

        for (final doc in hireRequestsSnapshot.docs) {
          final data = doc.data() as Map<String, dynamic>;

          // Skip if we already have a review for this hire request
          if (existingHireRequestIds.contains(doc.id)) {
            debugPrint('UserContentService - Skipping hire request ${doc.id} as it already has a review');
            continue;
          }

          // Get client info
          String reviewerName = 'Anonymous';
          String reviewerAvatar = '';

          try {
            if (data['clientId'] != null) {
              final clientDoc = await _firestore.collection('users').doc(data['clientId']).get();
              if (clientDoc.exists) {
                final clientData = clientDoc.data() as Map<String, dynamic>;
                reviewerName = clientData['displayName'] ?? 'Anonymous';
                reviewerAvatar = clientData['photoURL'] ?? '';
                debugPrint('UserContentService - Found client reviewer: $reviewerName');
              }
            }
          } catch (e) {
            debugPrint('UserContentService - Error getting client info: $e');
          }

          // Format timestamp
          DateTime reviewedAt = DateTime.now();
          if (data['reviewedAt'] != null) {
            if (data['reviewedAt'] is Timestamp) {
              reviewedAt = (data['reviewedAt'] as Timestamp).toDate();
            }
          }

          reviews.add({
            'id': doc.id,
            'reviewerId': data['clientId'] ?? '',
            'reviewerName': reviewerName,
            'reviewerAvatar': reviewerAvatar,
            'rating': data['rating'] ?? 5,
            'comment': data['review'] ?? '',
            'time': _formatTimestamp(reviewedAt),
            'timestamp': reviewedAt,
            'projectType': data['planType'] ?? 'Unknown',
            'projectName': data['planName'] ?? 'Project',
            'hireRequestId': doc.id,
            'source': 'hire_requests_collection',
          });
        }
      } catch (e) {
        debugPrint('UserContentService - Error querying hire_requests collection: $e');
      }

      // Sort reviews by timestamp (newest first)
      reviews.sort((a, b) => b['timestamp'].compareTo(a['timestamp']));

      debugPrint('UserContentService - Processed ${reviews.length} reviews total');
      return reviews;
    } catch (e) {
      debugPrint('UserContentService - Error getting user reviews: $e');
      return [];
    }
  }

  // Check if the reviews collection is accessible
  Future<bool> checkReviewsAccess() async {
    try {
      debugPrint('UserContentService - Checking reviews collection access');

      // Try to get a single document from the reviews collection
      final QuerySnapshot snapshot = await _firestore
          .collection('reviews')
          .limit(1)
          .get();

      debugPrint('UserContentService - Reviews collection access check: ${snapshot.docs.isNotEmpty ? 'Success' : 'Empty but accessible'}');
      return true;
    } catch (e) {
      debugPrint('UserContentService - Reviews collection access check failed: $e');
      return false;
    }
  }

  // Format timestamp for display
  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'Unknown time';

    DateTime dateTime;
    if (timestamp is Timestamp) {
      dateTime = timestamp.toDate();
    } else if (timestamp is DateTime) {
      dateTime = timestamp;
    } else {
      return 'Unknown time';
    }

    final DateTime now = DateTime.now();
    final Duration difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()}w ago';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()}mo ago';
    } else {
      return '${(difference.inDays / 365).floor()}y ago';
    }
  }

  // Report a post
  Future<Map<String, dynamic>> reportPost(String postId, String reason) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      final reportData = {
        'postId': postId,
        'reportedBy': user.uid,
        'reason': reason,
        'createdAt': FieldValue.serverTimestamp(),
        'status': 'pending', // pending, reviewed, resolved
      };

      await _firestore.collection('reported_posts').add(reportData);

      debugPrint('Post reported successfully: $postId');
      return {'success': true};
    } catch (e) {
      debugPrint('Error reporting post: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Hide posts from a specific user
  Future<bool> hidePostsFromUser(String authorId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        debugPrint('User not authenticated');
        return false;
      }

      // Add to hidden users list
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('hidden_users')
          .doc(authorId)
          .set({
        'hiddenAt': FieldValue.serverTimestamp(),
      });

      debugPrint('Successfully hid posts from user: $authorId');
      return true;
    } catch (e) {
      debugPrint('Error hiding posts from user: $e');
      return false;
    }
  }
}
